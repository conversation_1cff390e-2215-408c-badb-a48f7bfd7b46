package com.pharma.dms.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "document_versions")
public class DocumentVersion {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", nullable = false)
    private Document document;
    
    @Column(name = "version_number", nullable = false)
    private String versionNumber;
    
    @Column(name = "major_version", nullable = false)
    private Integer majorVersion;
    
    @Column(name = "minor_version", nullable = false)
    private Integer minorVersion;
    
    @Column(name = "patch_version")
    private Integer patchVersion;
    
    @Column(name = "file_path", nullable = false)
    private String filePath;
    
    @Column(name = "file_size")
    private Long fileSize;
    
    @Column(name = "mime_type")
    private String mimeType;
    
    @Column(name = "checksum")
    private String checksum;
    
    @Column(name = "change_description", columnDefinition = "TEXT")
    private String changeDescription;
    
    @Column(name = "change_reason")
    private String changeReason;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "change_type")
    private ChangeType changeType;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by", nullable = false)
    private User createdBy;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approved_by")
    private User approvedBy;
    
    @Column(name = "approved_at")
    private LocalDateTime approvedAt;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private VersionStatus status;
    
    @Column(name = "is_current", nullable = false)
    private Boolean isCurrent = false;
    
    @Column(name = "is_archived", nullable = false)
    private Boolean isArchived = false;
    
    @Column(name = "retention_date")
    private LocalDateTime retentionDate;
    
    @Column(name = "download_count")
    private Long downloadCount = 0L;
    
    @Column(name = "view_count")
    private Long viewCount = 0L;
    
    // GMP相关字段
    @Column(name = "gmp_impact_assessment", columnDefinition = "TEXT")
    private String gmpImpactAssessment;
    
    @Column(name = "training_required")
    private Boolean trainingRequired = false;
    
    @Column(name = "effective_date")
    private LocalDateTime effectiveDate;
    
    @Column(name = "superseded_date")
    private LocalDateTime supersededDate;
    
    public enum ChangeType {
        MAJOR,      // 重大变更，需要重新培训
        MINOR,      // 次要变更，可能需要通知
        PATCH,      // 补丁修复，通常不需要特殊处理
        CORRECTION, // 错误修正
        EDITORIAL   // 编辑性修改
    }
    
    public enum VersionStatus {
        DRAFT,          // 草稿
        UNDER_REVIEW,   // 审核中
        APPROVED,       // 已批准
        ACTIVE,         // 当前有效版本
        SUPERSEDED,     // 已被替代
        ARCHIVED,       // 已归档
        OBSOLETE        // 已废弃
    }
    
    // 构造函数
    public DocumentVersion() {
        this.createdAt = LocalDateTime.now();
        this.status = VersionStatus.DRAFT;
    }
    
    public DocumentVersion(Document document, String versionNumber, User createdBy) {
        this();
        this.document = document;
        this.versionNumber = versionNumber;
        this.createdBy = createdBy;
        parseVersionNumber(versionNumber);
    }
    
    // 解析版本号
    private void parseVersionNumber(String versionNumber) {
        if (versionNumber != null && versionNumber.matches("\\d+\\.\\d+(\\.\\d+)?")) {
            String[] parts = versionNumber.split("\\.");
            this.majorVersion = Integer.parseInt(parts[0]);
            this.minorVersion = Integer.parseInt(parts[1]);
            if (parts.length > 2) {
                this.patchVersion = Integer.parseInt(parts[2]);
            } else {
                this.patchVersion = 0;
            }
        }
    }
    
    // 生成下一个版本号
    public static String generateNextVersion(String currentVersion, ChangeType changeType) {
        if (currentVersion == null || currentVersion.isEmpty()) {
            return "1.0.0";
        }
        
        String[] parts = currentVersion.split("\\.");
        int major = Integer.parseInt(parts[0]);
        int minor = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
        int patch = parts.length > 2 ? Integer.parseInt(parts[2]) : 0;
        
        switch (changeType) {
            case MAJOR:
                return (major + 1) + ".0.0";
            case MINOR:
            case CORRECTION:
                return major + "." + (minor + 1) + ".0";
            case PATCH:
            case EDITORIAL:
            default:
                return major + "." + minor + "." + (patch + 1);
        }
    }
    
    // 比较版本号
    public int compareVersion(DocumentVersion other) {
        if (other == null) return 1;
        
        int majorCompare = this.majorVersion.compareTo(other.majorVersion);
        if (majorCompare != 0) return majorCompare;
        
        int minorCompare = this.minorVersion.compareTo(other.minorVersion);
        if (minorCompare != 0) return minorCompare;
        
        return this.patchVersion.compareTo(other.patchVersion);
    }
    
    // 检查是否需要GMP影响评估
    public boolean requiresGmpAssessment() {
        return changeType == ChangeType.MAJOR || changeType == ChangeType.MINOR;
    }
    
    // 检查是否需要培训
    public boolean requiresTraining() {
        return trainingRequired || changeType == ChangeType.MAJOR;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Document getDocument() { return document; }
    public void setDocument(Document document) { this.document = document; }
    
    public String getVersionNumber() { return versionNumber; }
    public void setVersionNumber(String versionNumber) { 
        this.versionNumber = versionNumber;
        parseVersionNumber(versionNumber);
    }
    
    public Integer getMajorVersion() { return majorVersion; }
    public void setMajorVersion(Integer majorVersion) { this.majorVersion = majorVersion; }
    
    public Integer getMinorVersion() { return minorVersion; }
    public void setMinorVersion(Integer minorVersion) { this.minorVersion = minorVersion; }
    
    public Integer getPatchVersion() { return patchVersion; }
    public void setPatchVersion(Integer patchVersion) { this.patchVersion = patchVersion; }
    
    public String getFilePath() { return filePath; }
    public void setFilePath(String filePath) { this.filePath = filePath; }
    
    public Long getFileSize() { return fileSize; }
    public void setFileSize(Long fileSize) { this.fileSize = fileSize; }
    
    public String getMimeType() { return mimeType; }
    public void setMimeType(String mimeType) { this.mimeType = mimeType; }
    
    public String getChecksum() { return checksum; }
    public void setChecksum(String checksum) { this.checksum = checksum; }
    
    public String getChangeDescription() { return changeDescription; }
    public void setChangeDescription(String changeDescription) { this.changeDescription = changeDescription; }
    
    public String getChangeReason() { return changeReason; }
    public void setChangeReason(String changeReason) { this.changeReason = changeReason; }
    
    public ChangeType getChangeType() { return changeType; }
    public void setChangeType(ChangeType changeType) { this.changeType = changeType; }
    
    public User getCreatedBy() { return createdBy; }
    public void setCreatedBy(User createdBy) { this.createdBy = createdBy; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public User getApprovedBy() { return approvedBy; }
    public void setApprovedBy(User approvedBy) { this.approvedBy = approvedBy; }
    
    public LocalDateTime getApprovedAt() { return approvedAt; }
    public void setApprovedAt(LocalDateTime approvedAt) { this.approvedAt = approvedAt; }
    
    public VersionStatus getStatus() { return status; }
    public void setStatus(VersionStatus status) { this.status = status; }
    
    public Boolean getIsCurrent() { return isCurrent; }
    public void setIsCurrent(Boolean isCurrent) { this.isCurrent = isCurrent; }
    
    public Boolean getIsArchived() { return isArchived; }
    public void setIsArchived(Boolean isArchived) { this.isArchived = isArchived; }
    
    public LocalDateTime getRetentionDate() { return retentionDate; }
    public void setRetentionDate(LocalDateTime retentionDate) { this.retentionDate = retentionDate; }
    
    public Long getDownloadCount() { return downloadCount; }
    public void setDownloadCount(Long downloadCount) { this.downloadCount = downloadCount; }
    
    public Long getViewCount() { return viewCount; }
    public void setViewCount(Long viewCount) { this.viewCount = viewCount; }
    
    public String getGmpImpactAssessment() { return gmpImpactAssessment; }
    public void setGmpImpactAssessment(String gmpImpactAssessment) { this.gmpImpactAssessment = gmpImpactAssessment; }
    
    public Boolean getTrainingRequired() { return trainingRequired; }
    public void setTrainingRequired(Boolean trainingRequired) { this.trainingRequired = trainingRequired; }
    
    public LocalDateTime getEffectiveDate() { return effectiveDate; }
    public void setEffectiveDate(LocalDateTime effectiveDate) { this.effectiveDate = effectiveDate; }
    
    public LocalDateTime getSupersededDate() { return supersededDate; }
    public void setSupersededDate(LocalDateTime supersededDate) { this.supersededDate = supersededDate; }
}
