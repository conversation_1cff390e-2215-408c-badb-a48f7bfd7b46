#!/bin/bash

# PostgreSQL 数据库启动脚本
# 适用于 Git Bash 环境

echo "=========================================="
echo "🚀 启动制药DMS数据库服务"
echo "=========================================="

# PostgreSQL 安装路径
POSTGRES_HOME="/d/sql/pgsql"
POSTGRES_BIN="$POSTGRES_HOME/bin"
POSTGRES_DATA="$POSTGRES_HOME/data"

# 检查PostgreSQL是否已安装
if [ ! -d "$POSTGRES_BIN" ]; then
    echo "❌ 错误: PostgreSQL未找到在路径 $POSTGRES_HOME"
    echo "请确认PostgreSQL安装路径是否正确"
    exit 1
fi

echo "📍 PostgreSQL路径: $POSTGRES_HOME"
echo "📊 数据目录: $POSTGRES_DATA"

# 检查PostgreSQL服务是否已经运行
echo "🔍 检查PostgreSQL服务状态..."
if pgrep -f postgres > /dev/null; then
    echo "✅ PostgreSQL服务已经在运行"
    echo "📋 当前PostgreSQL进程:"
    pgrep -f postgres | head -5
else
    echo "🔄 启动PostgreSQL服务..."
    
    # 启动PostgreSQL服务
    "$POSTGRES_BIN/pg_ctl" -D "$POSTGRES_DATA" -l "$POSTGRES_DATA/postgresql.log" start
    
    if [ $? -eq 0 ]; then
        echo "✅ PostgreSQL服务启动成功"
        sleep 3
    else
        echo "❌ PostgreSQL服务启动失败"
        echo "📋 查看日志文件: $POSTGRES_DATA/postgresql.log"
        exit 1
    fi
fi

# 检查数据库连接
echo "🔗 测试数据库连接..."
"$POSTGRES_BIN/psql" -U postgres -d postgres -c "SELECT version();" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 数据库连接测试成功"
else
    echo "⚠️  数据库连接测试失败，但服务可能正在启动中..."
fi

# 检查或创建pharma_dms数据库
echo "🗄️  检查pharma_dms数据库..."
DB_EXISTS=$("$POSTGRES_BIN/psql" -U postgres -tAc "SELECT 1 FROM pg_database WHERE datname='pharma_dms';" 2>/dev/null)

if [ "$DB_EXISTS" = "1" ]; then
    echo "✅ pharma_dms数据库已存在"
else
    echo "🔨 创建pharma_dms数据库..."
    "$POSTGRES_BIN/psql" -U postgres -c "CREATE DATABASE pharma_dms;" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "✅ pharma_dms数据库创建成功"
    else
        echo "⚠️  数据库创建可能失败，但应用程序会自动处理"
    fi
fi

echo "=========================================="
echo "✅ 数据库服务启动完成"
echo "📊 连接信息:"
echo "   主机: localhost"
echo "   端口: 5432"
echo "   数据库: pharma_dms"
echo "   用户: postgres"
echo "=========================================="
