package com.pharma.dms.service;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.imageio.ImageIO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.pharma.dms.config.AIConfig;

import jakarta.annotation.PostConstruct;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;

@Service("ocrServiceNew")
public class OCRServiceNew {

    private static final Logger logger = LoggerFactory.getLogger(OCRServiceNew.class);

    @Autowired
    private AIConfig.OCRProperties ocrProperties;

    private boolean tesseractAvailable = false;
    private Tesseract tesseract;

    @PostConstruct
    public void initializeTesseract() {
        try {
            tesseract = new Tesseract();
            
            // 设置Tesseract路径
            if (ocrProperties != null && ocrProperties.getTesseractPath() != null) {
                // 设置Tesseract可执行文件路径
                System.setProperty("jna.library.path", ocrProperties.getTesseractPath());
                // 设置tessdata路径
                tesseract.setDatapath(ocrProperties.getTesseractPath() + "/tessdata");
                logger.info("Tesseract path set to: {}", ocrProperties.getTesseractPath());
                logger.info("Tesseract data path set to: {}/tessdata", ocrProperties.getTesseractPath());
            }
            
            // 设置语言
            if (ocrProperties != null && ocrProperties.getLanguage() != null) {
                tesseract.setLanguage(ocrProperties.getLanguage());
                logger.info("Tesseract language set to: {}", ocrProperties.getLanguage());
            } else {
                tesseract.setLanguage("chi_sim+eng");
            }
            
            // 测试Tesseract是否可用
            testTesseract();
            
            tesseractAvailable = true;
            logger.info("Tesseract OCR initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize Tesseract OCR", e);
            tesseractAvailable = false;
        }
    }

    private void testTesseract() throws TesseractException {
        // 创建一个简单的测试图像
        BufferedImage testImage = new BufferedImage(100, 50, BufferedImage.TYPE_INT_RGB);
        tesseract.doOCR(testImage);
        logger.info("Tesseract test successful");
    }

    /**
     * 从图像文件中提取文本
     */
    @Cacheable(value = "ocrResults", key = "#imageBytes.hashCode()")
    public OCRResult extractTextFromImage(byte[] imageBytes, String fileName) {
        if (!isOCREnabled()) {
            return new OCRResult(false, "OCR功能未启用或Tesseract不可用", "", 0, List.of());
        }

        try {
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageBytes));
            if (image == null) {
                return new OCRResult(false, "无法读取图像文件", "", 0, List.of());
            }

            // 预处理图像
            BufferedImage processedImage = preprocessImage(image);

            // 执行OCR
            String extractedText = tesseract.doOCR(processedImage);
            
            if (extractedText == null || extractedText.trim().isEmpty()) {
                return new OCRResult(false, "未识别到文本内容", "", 0, List.of());
            }

            // 清理文本
            extractedText = cleanExtractedText(extractedText);

            // 计算置信度
            int confidence = calculateConfidence(extractedText);

            // 提取关键信息
            List<String> keywords = extractKeywords(extractedText);

            logger.info("OCR completed for file: {}, confidence: {}%", fileName, confidence);

            return new OCRResult(true, "OCR识别成功", extractedText, confidence, keywords);

        } catch (TesseractException e) {
            logger.error("Tesseract OCR failed for file: {}", fileName, e);
            return new OCRResult(false, "OCR识别失败: " + e.getMessage(), "", 0, List.of());
        } catch (IOException e) {
            logger.error("Image processing failed for file: {}", fileName, e);
            return new OCRResult(false, "图像处理失败: " + e.getMessage(), "", 0, List.of());
        } catch (Exception e) {
            logger.error("Unexpected error during OCR for file: {}", fileName, e);
            return new OCRResult(false, "OCR处理出现未知错误", "", 0, List.of());
        }
    }

    /**
     * 批量处理多个图像文件
     */
    public List<OCRResult> batchProcessImages(List<byte[]> imageBytesList, List<String> fileNames) {
        List<OCRResult> results = new ArrayList<>();
        
        for (int i = 0; i < imageBytesList.size(); i++) {
            byte[] imageBytes = imageBytesList.get(i);
            String fileName = (i < fileNames.size()) ? fileNames.get(i) : "image_" + i;
            
            OCRResult result = extractTextFromImage(imageBytes, fileName);
            results.add(result);
        }
        
        return results;
    }

    /**
     * 图像预处理（提高OCR准确性）
     */
    private BufferedImage preprocessImage(BufferedImage image) {
        // 这里可以添加图像预处理逻辑：
        // 1. 灰度化
        // 2. 二值化
        // 3. 降噪
        // 4. 倾斜校正
        // 目前返回原图像
        return image;
    }

    /**
     * 清理提取的文本
     */
    private String cleanExtractedText(String text) {
        if (text == null) return "";
        
        return text
                .replaceAll("\\s+", " ")  // 多个空白字符替换为单个空格
                .replaceAll("[\r\n]+", "\n")  // 多个换行符替换为单个换行符
                .trim();
    }

    /**
     * 计算OCR置信度
     */
    private int calculateConfidence(String text) {
        if (text == null || text.trim().isEmpty()) {
            return 0;
        }
        
        // 简化的置信度计算：基于文本长度和字符类型
        int length = text.length();
        int chineseChars = 0;
        int englishChars = 0;
        int numbers = 0;
        int specialChars = 0;
        
        for (char c : text.toCharArray()) {
            if (Character.toString(c).matches("[\u4e00-\u9fa5]")) {
                chineseChars++;
            } else if (Character.isLetter(c)) {
                englishChars++;
            } else if (Character.isDigit(c)) {
                numbers++;
            } else if (!Character.isWhitespace(c)) {
                specialChars++;
            }
        }
        
        // 基于字符分布计算置信度
        int confidence = Math.min(95, 30 + (chineseChars + englishChars + numbers) * 2);
        return Math.max(confidence, 10); // 最低10%置信度
    }

    /**
     * 从文本中提取关键词
     */
    private List<String> extractKeywords(String text) {
        List<String> keywords = new ArrayList<>();
        
        if (text == null || text.trim().isEmpty()) {
            return keywords;
        }
        
        // 简化的关键词提取：查找常见的制药术语
        String[] pharmaTerms = {
            "GMP", "质量", "验证", "标准", "程序", "SOP", "批次", "检验",
            "药品", "生产", "质量控制", "QC", "QA", "FDA", "CFDA", "NMPA",
            "验收", "清洁", "消毒", "灭菌", "包装", "标签", "追溯"
        };
        
        String upperText = text.toUpperCase();
        for (String term : pharmaTerms) {
            if (upperText.contains(term.toUpperCase())) {
                keywords.add(term);
            }
        }
        
        return keywords;
    }

    /**
     * 检查OCR功能是否可用
     */
    public boolean isOCREnabled() {
        return ocrProperties != null &&
               ocrProperties.getEnabled() != null &&
               ocrProperties.getEnabled() &&
               tesseractAvailable;
    }

    /**
     * 获取OCR配置信息
     */
    public OCRConfigInfo getOCRConfigInfo() {
        return new OCRConfigInfo(
                isOCREnabled(),
                ocrProperties != null ? ocrProperties.getLanguage() : "未配置",
                ocrProperties != null ? ocrProperties.getConfidenceThreshold() : 0,
                tesseractAvailable ? "Tesseract已初始化" : "Tesseract未初始化"
        );
    }

    // Result classes
    public static class OCRResult {
        private final boolean success;
        private final String message;
        private final String extractedText;
        private final int confidence;
        private final List<String> keywords;

        public OCRResult(boolean success, String message, String extractedText, int confidence, List<String> keywords) {
            this.success = success;
            this.message = message;
            this.extractedText = extractedText;
            this.confidence = confidence;
            this.keywords = keywords;
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public String getExtractedText() { return extractedText; }
        public int getConfidence() { return confidence; }
        public List<String> getKeywords() { return keywords; }
    }

    public static class OCRConfigInfo {
        private final boolean enabled;
        private final String language;
        private final int confidenceThreshold;
        private final String status;

        public OCRConfigInfo(boolean enabled, String language, int confidenceThreshold, String status) {
            this.enabled = enabled;
            this.language = language;
            this.confidenceThreshold = confidenceThreshold;
            this.status = status;
        }

        // Getters
        public boolean isEnabled() { return enabled; }
        public String getLanguage() { return language; }
        public int getConfidenceThreshold() { return confidenceThreshold; }
        public String getStatus() { return status; }
    }
}
