package com.pharma.dms.repository;

import com.pharma.dms.entity.TrainingRecord;
import com.pharma.dms.entity.TrainingCourse;
import com.pharma.dms.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TrainingRecordRepository extends JpaRepository<TrainingRecord, Long> {

    // Basic queries
    List<TrainingRecord> findByUser(User user);
    
    List<TrainingRecord> findByCourse(TrainingCourse course);
    
    Optional<TrainingRecord> findByCourseAndUser(TrainingCourse course, User user);
    
    List<TrainingRecord> findByStatus(TrainingRecord.TrainingStatus status);
    
    List<TrainingRecord> findByUserAndStatus(User user, TrainingRecord.TrainingStatus status);

    // Certificate queries
    Optional<TrainingRecord> findByCertificateNumber(String certificateNumber);
    
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.certificateExpiryDate BETWEEN :startDate AND :endDate")
    List<TrainingRecord> findCertificatesExpiringBetween(@Param("startDate") LocalDateTime startDate,
                                                        @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.certificateExpiryDate < :date AND tr.status = 'CERTIFIED'")
    List<TrainingRecord> findExpiredCertificates(@Param("date") LocalDateTime date);

    // Completion and progress queries
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.user.id = :userId AND tr.status = 'COMPLETED'")
    List<TrainingRecord> findCompletedTrainingsByUser(@Param("userId") Long userId);
    
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.user.id = :userId AND tr.status = 'IN_PROGRESS'")
    List<TrainingRecord> findInProgressTrainingsByUser(@Param("userId") Long userId);
    
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.user.id = :userId AND tr.finalScore >= tr.passingScore")
    List<TrainingRecord> findPassedTrainingsByUser(@Param("userId") Long userId);

    // Search and filtering
    @Query("SELECT tr FROM TrainingRecord tr WHERE " +
           "(:userId IS NULL OR tr.user.id = :userId) AND " +
           "(:courseId IS NULL OR tr.course.id = :courseId) AND " +
           "(:status IS NULL OR tr.status = :status) AND " +
           "(:startDate IS NULL OR tr.startDate >= :startDate) AND " +
           "(:endDate IS NULL OR tr.completionDate <= :endDate) AND " +
           "(:instructorId IS NULL OR tr.instructor.id = :instructorId)")
    Page<TrainingRecord> findTrainingRecordsWithFilters(@Param("userId") Long userId,
                                                       @Param("courseId") Long courseId,
                                                       @Param("status") TrainingRecord.TrainingStatus status,
                                                       @Param("startDate") LocalDateTime startDate,
                                                       @Param("endDate") LocalDateTime endDate,
                                                       @Param("instructorId") Long instructorId,
                                                       Pageable pageable);

    // Department-based queries
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.user.department.id = :departmentId")
    List<TrainingRecord> findByUserDepartment(@Param("departmentId") Long departmentId);
    
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.user.department.id = :departmentId AND tr.status = :status")
    List<TrainingRecord> findByUserDepartmentAndStatus(@Param("departmentId") Long departmentId,
                                                      @Param("status") TrainingRecord.TrainingStatus status);

    // Statistics queries
    @Query("SELECT COUNT(tr) FROM TrainingRecord tr WHERE tr.user.id = :userId")
    Long countByUser(@Param("userId") Long userId);
    
    @Query("SELECT COUNT(tr) FROM TrainingRecord tr WHERE tr.course.id = :courseId")
    Long countByCourse(@Param("courseId") Long courseId);
    
    @Query("SELECT COUNT(tr) FROM TrainingRecord tr WHERE tr.status = :status")
    Long countByStatus(@Param("status") TrainingRecord.TrainingStatus status);
    
    @Query("SELECT COUNT(tr) FROM TrainingRecord tr WHERE tr.user.id = :userId AND tr.status = 'COMPLETED'")
    Long countCompletedByUser(@Param("userId") Long userId);
    
    @Query("SELECT COUNT(tr) FROM TrainingRecord tr WHERE tr.course.id = :courseId AND tr.status = 'COMPLETED'")
    Long countCompletedByCourse(@Param("courseId") Long courseId);

    // Average scores
    @Query("SELECT AVG(tr.finalScore) FROM TrainingRecord tr WHERE tr.course.id = :courseId AND tr.finalScore IS NOT NULL")
    Double getAverageScoreByCourse(@Param("courseId") Long courseId);
    
    @Query("SELECT AVG(tr.finalScore) FROM TrainingRecord tr WHERE tr.user.id = :userId AND tr.finalScore IS NOT NULL")
    Double getAverageScoreByUser(@Param("userId") Long userId);

    // Retrain requirements
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.isRetrainRequired = true")
    List<TrainingRecord> findRetrainRequired();
    
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.retrainDueDate BETWEEN :startDate AND :endDate")
    List<TrainingRecord> findRetrainDueBetween(@Param("startDate") LocalDateTime startDate,
                                             @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.retrainDueDate < :date AND tr.isRetrainRequired = true")
    List<TrainingRecord> findOverdueRetraining(@Param("date") LocalDateTime date);

    // Electronic signature queries
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.electronicSignature IS NOT NULL")
    List<TrainingRecord> findRecordsWithElectronicSignature();
    
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.signatureDate BETWEEN :startDate AND :endDate")
    List<TrainingRecord> findRecordsSignedBetween(@Param("startDate") LocalDateTime startDate,
                                                 @Param("endDate") LocalDateTime endDate);

    // Recent activity
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.startDate >= :since ORDER BY tr.startDate DESC")
    List<TrainingRecord> findRecentlyStarted(@Param("since") LocalDateTime since, Pageable pageable);
    
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.completionDate >= :since ORDER BY tr.completionDate DESC")
    List<TrainingRecord> findRecentlyCompleted(@Param("since") LocalDateTime since, Pageable pageable);

    // Compliance queries
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.course.isMandatory = true AND tr.user.id = :userId")
    List<TrainingRecord> findMandatoryTrainingsByUser(@Param("userId") Long userId);
    
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.course.isMandatory = true AND " +
           "tr.user.id = :userId AND tr.status NOT IN ('COMPLETED', 'CERTIFIED')")
    List<TrainingRecord> findIncompleteMandatoryTrainingsByUser(@Param("userId") Long userId);

    // Performance queries
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.finalScore < tr.passingScore AND tr.attemptsCount >= tr.maxAttempts")
    List<TrainingRecord> findFailedTrainings();
    
    @Query("SELECT tr FROM TrainingRecord tr ORDER BY tr.finalScore DESC")
    List<TrainingRecord> findTopPerformers(Pageable pageable);
    
    @Query("SELECT tr FROM TrainingRecord tr WHERE tr.totalTimeMinutes IS NOT NULL ORDER BY tr.totalTimeMinutes ASC")
    List<TrainingRecord> findFastestCompletions(Pageable pageable);
}
