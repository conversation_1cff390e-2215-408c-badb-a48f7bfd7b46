#!/bin/bash

# 制药DMS数据库初始化脚本

echo "🗄️ 初始化制药DMS数据库..."

# PostgreSQL路径
POSTGRES_BIN="/d/sql/pgsql/bin"
DB_NAME="pharma_dms"
DB_USER="postgres"

# 检查PostgreSQL是否运行
if ! pgrep -f postgres > /dev/null; then
    echo "🔄 启动PostgreSQL..."
    /d/sql/pgsql/bin/pg_ctl -D /d/sql/pgsql/data start
    sleep 5
fi

# 检查数据库是否存在
echo "🔍 检查数据库是否存在..."
DB_EXISTS=$("$POSTGRES_BIN/psql" -U "$DB_USER" -tAc "SELECT 1 FROM pg_database WHERE datname='$DB_NAME';" 2>/dev/null)

if [ "$DB_EXISTS" = "1" ]; then
    echo "✅ 数据库 $DB_NAME 已存在"
else
    echo "🔨 创建数据库 $DB_NAME..."
    "$POSTGRES_BIN/psql" -U "$DB_USER" -c "CREATE DATABASE $DB_NAME;" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "✅ 数据库创建成功"
    else
        echo "❌ 数据库创建失败"
        exit 1
    fi
fi

# 验证数据库连接
echo "🔗 测试数据库连接..."
"$POSTGRES_BIN/psql" -U "$DB_USER" -d "$DB_NAME" -c "SELECT version();" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 数据库连接测试成功"
else
    echo "❌ 数据库连接测试失败"
    exit 1
fi

echo "🎉 数据库初始化完成!"
echo "📊 数据库信息:"
echo "   名称: $DB_NAME"
echo "   用户: $DB_USER"
echo "   主机: localhost"
echo "   端口: 5432"
