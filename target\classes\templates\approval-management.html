<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审批管理 - 制药DMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .approval-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }
        .approval-card:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
            transform: translateY(-2px);
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #0d6efd, #0056b3);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" style="min-height: 100vh; background-color: #f8f9fa; border-right: 1px solid #dee2e6;">
                <div class="position-sticky pt-3">
                    <div class="navbar-brand mb-4" style="font-weight: bold; color: #0d6efd !important;">
                        <i class="fas fa-pills me-2"></i>制药文档管理系统
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-tachometer-alt me-2"></i>仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/users" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-users me-2"></i>用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/documents" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-file-alt me-2"></i>文档管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/approval-management" style="background-color: #0d6efd; color: white !important; border-radius: 0.375rem;">
                                <i class="fas fa-check-circle me-2"></i>审批管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/training-courses" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-graduation-cap me-2"></i>培训课程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/training-assignment" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-clipboard-list me-2"></i>培训分配
                            </a>
                        </li>
                    </ul>

                    <hr>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/profile" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user me-2"></i>个人资料
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-sign-out-alt me-2"></i>退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-check-circle me-2 text-primary"></i>审批管理
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button class="btn btn-outline-primary" onclick="refreshApprovals()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button class="btn btn-outline-info" onclick="showApprovalStats()">
                                <i class="fas fa-chart-bar me-1"></i>统计
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 审批类型选项卡 -->
                <ul class="nav nav-pills mb-4" id="approvalTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="document-tab" data-bs-toggle="pill" data-bs-target="#document-approvals" type="button" role="tab">
                            <i class="fas fa-file-alt me-2"></i>文档审批
                            <span class="badge bg-danger ms-1" id="documentApprovalCount">0</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="training-tab" data-bs-toggle="pill" data-bs-target="#training-approvals" type="button" role="tab">
                            <i class="fas fa-graduation-cap me-2"></i>培训审批
                            <span class="badge bg-warning ms-1" id="trainingApprovalCount">0</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="history-tab" data-bs-toggle="pill" data-bs-target="#approval-history" type="button" role="tab">
                            <i class="fas fa-history me-2"></i>审批历史
                        </button>
                    </li>
                </ul>

                <!-- 审批内容 -->
                <div class="tab-content" id="approvalTabContent">
                    <!-- 文档审批 -->
                    <div class="tab-pane fade show active" id="document-approvals" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-alt me-2"></i>待审批文档
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="documentApprovalsList">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <div class="mt-2">正在加载待审批文档...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 培训审批 -->
                    <div class="tab-pane fade" id="training-approvals" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-graduation-cap me-2"></i>待审批培训课程
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="trainingApprovalsList">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <div class="mt-2">正在加载待审批培训课程...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 审批历史 -->
                    <div class="tab-pane fade" id="approval-history" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-history me-2"></i>审批历史记录
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="approvalHistoryList">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <div class="mt-2">正在加载审批历史...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 审批模态框 -->
    <div class="modal fade" id="approvalModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="approvalModalTitle">审批操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="approvalModalContent">
                    <!-- 审批内容将在这里动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/dms/js/auth.js"></script>
    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDocumentApprovals();
            loadTrainingApprovals();
        });

        // 加载待审批文档
        async function loadDocumentApprovals() {
            try {
                const response = await authUtils.secureApiCall('/dms/api/document-approvals/pending?page=0&size=20');
                
                if (response.ok) {
                    const result = await response.json();
                    displayDocumentApprovals(result.data.content);
                    document.getElementById('documentApprovalCount').textContent = result.data.totalElements;
                } else {
                    document.getElementById('documentApprovalsList').innerHTML = 
                        '<div class="alert alert-warning">无法加载待审批文档</div>';
                }
            } catch (error) {
                console.error('加载文档审批失败:', error);
                document.getElementById('documentApprovalsList').innerHTML = 
                    '<div class="alert alert-danger">加载失败: ' + error.message + '</div>';
            }
        }

        // 显示文档审批列表
        function displayDocumentApprovals(documents) {
            const container = document.getElementById('documentApprovalsList');
            
            if (documents.length === 0) {
                container.innerHTML = '<div class="alert alert-info">暂无待审批文档</div>';
                return;
            }

            let html = '<div class="row">';
            documents.forEach(doc => {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card approval-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">${doc.title}</h6>
                                    <span class="badge bg-warning status-badge">待审批</span>
                                </div>
                                <p class="card-text text-muted small">${doc.description || '无描述'}</p>
                                <div class="row text-muted small mb-3">
                                    <div class="col-6">
                                        <i class="fas fa-user me-1"></i>${doc.ownerName}
                                    </div>
                                    <div class="col-6">
                                        <i class="fas fa-clock me-1"></i>${formatDate(doc.reviewDate)}
                                    </div>
                                </div>
                                <div class="btn-group w-100" role="group">
                                    <button class="btn btn-outline-primary btn-sm" onclick="viewDocumentDetails(${doc.id})">
                                        <i class="fas fa-eye me-1"></i>查看
                                    </button>
                                    <button class="btn btn-success btn-sm" onclick="approveDocument(${doc.id})">
                                        <i class="fas fa-check me-1"></i>批准
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="rejectDocument(${doc.id})">
                                        <i class="fas fa-times me-1"></i>拒绝
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            container.innerHTML = html;
        }

        // 加载待审批培训课程
        async function loadTrainingApprovals() {
            try {
                const response = await authUtils.secureApiCall('/dms/api/training-courses/awaiting-approval');
                
                if (response.ok) {
                    const result = await response.json();
                    displayTrainingApprovals(result.data);
                    document.getElementById('trainingApprovalCount').textContent = result.data.length;
                } else {
                    document.getElementById('trainingApprovalsList').innerHTML = 
                        '<div class="alert alert-warning">无法加载待审批培训课程</div>';
                }
            } catch (error) {
                console.error('加载培训审批失败:', error);
                document.getElementById('trainingApprovalsList').innerHTML = 
                    '<div class="alert alert-danger">加载失败: ' + error.message + '</div>';
            }
        }

        // 显示培训审批列表
        function displayTrainingApprovals(courses) {
            const container = document.getElementById('trainingApprovalsList');
            
            if (courses.length === 0) {
                container.innerHTML = '<div class="alert alert-info">暂无待审批培训课程</div>';
                return;
            }

            let html = '<div class="row">';
            courses.forEach(course => {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card approval-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">${course.title}</h6>
                                    <span class="badge bg-warning status-badge">待审批</span>
                                </div>
                                <p class="card-text text-muted small">${course.description || '无描述'}</p>
                                <div class="row text-muted small mb-3">
                                    <div class="col-6">
                                        <i class="fas fa-clock me-1"></i>${course.durationMinutes}分钟
                                    </div>
                                    <div class="col-6">
                                        <i class="fas fa-tag me-1"></i>${course.courseType}
                                    </div>
                                </div>
                                <div class="btn-group w-100" role="group">
                                    <button class="btn btn-outline-primary btn-sm" onclick="viewCourseDetails(${course.id})">
                                        <i class="fas fa-eye me-1"></i>查看
                                    </button>
                                    <button class="btn btn-success btn-sm" onclick="approveCourse(${course.id})">
                                        <i class="fas fa-check me-1"></i>批准
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="rejectCourse(${course.id})">
                                        <i class="fas fa-times me-1"></i>拒绝
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            container.innerHTML = html;
        }

        // 审批操作函数
        async function approveDocument(documentId) {
            const notes = prompt('请输入审批意见（可选）:');
            if (notes === null) return; // 用户取消

            try {
                const response = await authUtils.secureApiCall(`/dms/api/document-approvals/${documentId}/approve`, {
                    method: 'POST',
                    body: JSON.stringify({ approvalNotes: notes })
                });

                if (response.ok) {
                    alert('文档审批成功！');
                    loadDocumentApprovals();
                } else {
                    const result = await response.json();
                    alert('审批失败: ' + result.message);
                }
            } catch (error) {
                alert('审批失败: ' + error.message);
            }
        }

        async function rejectDocument(documentId) {
            const notes = prompt('请输入拒绝原因:');
            if (!notes) {
                alert('请输入拒绝原因');
                return;
            }

            try {
                const response = await authUtils.secureApiCall(`/dms/api/document-approvals/${documentId}/reject`, {
                    method: 'POST',
                    body: JSON.stringify({ rejectionNotes: notes })
                });

                if (response.ok) {
                    alert('文档已拒绝！');
                    loadDocumentApprovals();
                } else {
                    const result = await response.json();
                    alert('拒绝失败: ' + result.message);
                }
            } catch (error) {
                alert('拒绝失败: ' + error.message);
            }
        }

        async function approveCourse(courseId) {
            if (confirm('确定要批准这个培训课程吗？')) {
                try {
                    const response = await authUtils.secureApiCall(`/dms/api/training-courses/${courseId}/approve`, {
                        method: 'POST'
                    });

                    if (response.ok) {
                        alert('培训课程审批成功！');
                        loadTrainingApprovals();
                    } else {
                        const result = await response.json();
                        alert('审批失败: ' + result.message);
                    }
                } catch (error) {
                    alert('审批失败: ' + error.message);
                }
            }
        }

        function rejectCourse(courseId) {
            alert('培训课程拒绝功能开发中');
        }

        function viewDocumentDetails(documentId) {
            alert('查看文档详情功能开发中');
        }

        function viewCourseDetails(courseId) {
            alert('查看课程详情功能开发中');
        }

        function refreshApprovals() {
            loadDocumentApprovals();
            loadTrainingApprovals();
            alert('审批列表已刷新');
        }

        function showApprovalStats() {
            alert('审批统计功能开发中');
        }

        function formatDate(dateString) {
            if (!dateString) return '未知';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }
    </script>
</body>
</html>
