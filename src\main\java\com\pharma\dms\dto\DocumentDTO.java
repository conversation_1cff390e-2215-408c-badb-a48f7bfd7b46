package com.pharma.dms.dto;

import java.time.LocalDateTime;

import com.pharma.dms.entity.Document;

public class DocumentDTO {
    private Long id;
    private String title;
    private String description;
    private String fileName;
    private String originalFileName;
    private Long fileSize;
    private String mimeType;
    private String fileExtension;
    private Document.DocumentStatus status;
    private Document.DocumentClassification classification;
    private Integer versionNumber;
    private String versionLabel;
    private Boolean isCurrentVersion;
    private LocalDateTime expiryDate;
    private LocalDateTime reviewDate;
    private LocalDateTime approvalDate;
    private Long downloadCount;
    private Long viewCount;
    private String checksum;
    private Boolean isEncrypted;
    private Integer retentionPeriodMonths;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 简化的关联对象信息
    private String categoryName;
    private String ownerUsername;
    private String approvedByUsername;

    // 构造函数
    public DocumentDTO() {}

    public DocumentDTO(Document document) {
        this.id = document.getId();
        this.title = document.getTitle();
        this.description = document.getDescription();
        this.fileName = document.getFileName();
        this.originalFileName = document.getOriginalFileName();
        this.fileSize = document.getFileSize();
        this.mimeType = document.getMimeType();
        this.fileExtension = document.getFileExtension();
        this.status = document.getStatus();
        this.classification = document.getClassification();
        this.versionNumber = document.getVersionNumber();
        this.versionLabel = document.getVersionLabel();
        this.isCurrentVersion = document.getIsCurrentVersion();
        this.expiryDate = document.getExpiryDate();
        this.reviewDate = document.getReviewDate();
        this.approvalDate = document.getApprovalDate();
        this.downloadCount = document.getDownloadCount();
        this.viewCount = document.getViewCount();
        this.checksum = document.getChecksum();
        this.isEncrypted = document.getIsEncrypted();
        this.retentionPeriodMonths = document.getRetentionPeriodMonths();
        this.createdAt = document.getCreatedAt();
        this.updatedAt = document.getUpdatedAt();
        
        // 安全地获取关联对象信息
        if (document.getCategory() != null) {
            this.categoryName = document.getCategory().getName();
        }
        if (document.getOwner() != null) {
            this.ownerUsername = document.getOwner().getUsername();
        }
        if (document.getApprovedBy() != null) {
            this.approvedByUsername = document.getApprovedBy().getUsername();
        }
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }

    public String getOriginalFileName() { return originalFileName; }
    public void setOriginalFileName(String originalFileName) { this.originalFileName = originalFileName; }

    public Long getFileSize() { return fileSize; }
    public void setFileSize(Long fileSize) { this.fileSize = fileSize; }

    public String getMimeType() { return mimeType; }
    public void setMimeType(String mimeType) { this.mimeType = mimeType; }

    public String getFileExtension() { return fileExtension; }
    public void setFileExtension(String fileExtension) { this.fileExtension = fileExtension; }

    public Document.DocumentStatus getStatus() { return status; }
    public void setStatus(Document.DocumentStatus status) { this.status = status; }

    public Document.DocumentClassification getClassification() { return classification; }
    public void setClassification(Document.DocumentClassification classification) { this.classification = classification; }

    public Integer getVersionNumber() { return versionNumber; }
    public void setVersionNumber(Integer versionNumber) { this.versionNumber = versionNumber; }

    public String getVersionLabel() { return versionLabel; }
    public void setVersionLabel(String versionLabel) { this.versionLabel = versionLabel; }

    public Boolean getIsCurrentVersion() { return isCurrentVersion; }
    public void setIsCurrentVersion(Boolean isCurrentVersion) { this.isCurrentVersion = isCurrentVersion; }

    public LocalDateTime getExpiryDate() { return expiryDate; }
    public void setExpiryDate(LocalDateTime expiryDate) { this.expiryDate = expiryDate; }

    public LocalDateTime getReviewDate() { return reviewDate; }
    public void setReviewDate(LocalDateTime reviewDate) { this.reviewDate = reviewDate; }

    public LocalDateTime getApprovalDate() { return approvalDate; }
    public void setApprovalDate(LocalDateTime approvalDate) { this.approvalDate = approvalDate; }

    public Long getDownloadCount() { return downloadCount; }
    public void setDownloadCount(Long downloadCount) { this.downloadCount = downloadCount; }

    public Long getViewCount() { return viewCount; }
    public void setViewCount(Long viewCount) { this.viewCount = viewCount; }

    public String getChecksum() { return checksum; }
    public void setChecksum(String checksum) { this.checksum = checksum; }

    public Boolean getIsEncrypted() { return isEncrypted; }
    public void setIsEncrypted(Boolean isEncrypted) { this.isEncrypted = isEncrypted; }

    public Integer getRetentionPeriodMonths() { return retentionPeriodMonths; }
    public void setRetentionPeriodMonths(Integer retentionPeriodMonths) { this.retentionPeriodMonths = retentionPeriodMonths; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public String getCategoryName() { return categoryName; }
    public void setCategoryName(String categoryName) { this.categoryName = categoryName; }

    public String getOwnerUsername() { return ownerUsername; }
    public void setOwnerUsername(String ownerUsername) { this.ownerUsername = ownerUsername; }

    public String getApprovedByUsername() { return approvedByUsername; }
    public void setApprovedByUsername(String approvedByUsername) { this.approvedByUsername = approvedByUsername; }

    public String getFormattedFileSize() {
        if (fileSize == null) return "0 B";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", size, units[unitIndex]);
    }
}
