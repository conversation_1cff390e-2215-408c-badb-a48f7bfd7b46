@echo off
title 制药DMS系统启动器
color 0A

echo ========================================
echo    制药文档管理系统 (Pharmaceutical DMS)
echo    一键启动脚本
echo ========================================
echo.

cd /d D:\learn\java\dms

echo 检查Git Bash环境...
where bash >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到Git Bash
    echo 请确认Git已安装并在PATH环境变量中
    echo.
    echo 如果Git已安装，请手动打开Git Bash并执行:
    echo   cd /d/learn/java/dms
    echo   bash start-dms.sh
    echo.
    pause
    exit /b 1
)

echo 启动制药DMS系统...
echo.

REM 使用Git Bash执行启动脚本
bash -c "cd /d/learn/java/dms && bash start-dms.sh"

echo.
echo 启动完成!
echo 访问地址: http://172.100.15.120:8081/dms/login
echo.
pause
