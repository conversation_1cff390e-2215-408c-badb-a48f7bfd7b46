<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报表管理 - 制药文档管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        制药文档管理系统
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/users" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/documents" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-file-alt me-2"></i>
                                文档管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/system-overview" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-line me-2"></i>
                                系统概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/my-training" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user-graduate me-2"></i>
                                我的培训
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/training-courses" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-graduation-cap me-2"></i>
                                培训课程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/reports" style="background-color: #0d6efd; color: white !important; border-radius: 0.375rem;">
                                <i class="fas fa-chart-bar me-2"></i>
                                报表管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/settings" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-cog me-2"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>

                    <hr>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/profile" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user me-2"></i>
                                个人资料
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">报表管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button class="btn btn-success" onclick="exportReport()">
                                <i class="fas fa-download me-2"></i>导出报表
                            </button>
                            <button class="btn btn-primary" onclick="refreshReports()">
                                <i class="fas fa-sync me-2"></i>刷新数据
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Report Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            活跃用户</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeUsers">-</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            文档总数</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalDocuments">-</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            培训完成率</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="trainingPassRate">-</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-graduation-cap fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            平均分数</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="averageScore">-</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-star fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="row">
                    <!-- User Activity Chart -->
                    <div class="col-xl-6 col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">用户活动趋势</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="userActivityChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Document Category Chart -->
                    <div class="col-xl-6 col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">文档类型分布</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="documentCategoryChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Training Completion Chart -->
                <div class="row">
                    <div class="col-xl-12">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">培训完成趋势</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="trainingCompletionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        let userActivityChart, documentCategoryChart, trainingCompletionChart;

        document.addEventListener('DOMContentLoaded', function() {
            loadReportData();
        });

        async function loadReportData() {
            try {
                // Load all report data
                await Promise.all([
                    loadUserActivityReport(),
                    loadDocumentStatsReport(),
                    loadTrainingStatsReport()
                ]);
            } catch (error) {
                console.error('Error loading report data:', error);
            }
        }

        async function loadUserActivityReport() {
            try {
                const response = await authUtils.secureApiCall('/dms/api/reports/user-activity');

                if (response.ok) {
                    const result = await response.json();
                    const data = result.data;
                    
                    document.getElementById('activeUsers').textContent = data.activeUsers;
                    
                    // Create user activity chart
                    createUserActivityChart(data.dailyActivity);
                }
            } catch (error) {
                console.error('Error loading user activity report:', error);
            }
        }

        async function loadDocumentStatsReport() {
            try {
                const response = await authUtils.secureApiCall('/dms/api/reports/document-stats');

                if (response.ok) {
                    const result = await response.json();
                    const data = result.data;
                    
                    document.getElementById('totalDocuments').textContent = data.totalDocuments;
                    
                    // Create document category chart
                    createDocumentCategoryChart(data.byCategory);
                }
            } catch (error) {
                console.error('Error loading document stats report:', error);
            }
        }

        async function loadTrainingStatsReport() {
            try {
                const response = await authUtils.secureApiCall('/dms/api/reports/training-stats');

                if (response.ok) {
                    const result = await response.json();
                    const data = result.data;
                    
                    document.getElementById('trainingPassRate').textContent = data.passRate + '%';
                    document.getElementById('averageScore').textContent = data.averageScore;
                    
                    // Create training completion chart
                    createTrainingCompletionChart(data.completionTrend);
                }
            } catch (error) {
                console.error('Error loading training stats report:', error);
            }
        }

        function createUserActivityChart(data) {
            const ctx = document.getElementById('userActivityChart').getContext('2d');
            userActivityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(item => item.date),
                    datasets: [{
                        label: '活跃用户',
                        data: data.map(item => item.users),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function createDocumentCategoryChart(data) {
            const ctx = document.getElementById('documentCategoryChart').getContext('2d');
            documentCategoryChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: data.map(item => item.category),
                    datasets: [{
                        data: data.map(item => item.count),
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function createTrainingCompletionChart(data) {
            const ctx = document.getElementById('trainingCompletionChart').getContext('2d');
            trainingCompletionChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.map(item => item.month),
                    datasets: [{
                        label: '完成培训',
                        data: data.map(item => item.completed),
                        backgroundColor: 'rgba(54, 162, 235, 0.8)'
                    }, {
                        label: '通过培训',
                        data: data.map(item => item.passed),
                        backgroundColor: 'rgba(75, 192, 192, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function refreshReports() {
            loadReportData();
            alert('报表数据已刷新');
        }

        function exportReport() {
            alert('导出功能开发中...');
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 认证工具 -->
    <script src="/dms/js/auth.js"></script>
</body>
</html>
