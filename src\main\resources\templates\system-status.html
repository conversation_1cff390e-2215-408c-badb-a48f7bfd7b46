<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态 - 制药DMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-heartbeat me-2 text-success"></i>系统状态检查
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button class="btn btn-outline-primary" onclick="checkAllSystems()">
                            <i class="fas fa-sync-alt me-1"></i>刷新状态
                        </button>
                    </div>
                </div>

                <!-- 系统状态卡片 -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-server fa-2x text-primary mb-2"></i>
                                <h5 class="card-title">应用程序</h5>
                                <p class="card-text">
                                    <span class="badge bg-success" id="appStatus">运行中</span>
                                </p>
                                <small class="text-muted">端口: 8081</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-database fa-2x text-info mb-2"></i>
                                <h5 class="card-title">数据库</h5>
                                <p class="card-text">
                                    <span class="badge bg-success" id="dbStatus">H2内存数据库</span>
                                </p>
                                <small class="text-muted">连接正常</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-envelope fa-2x text-warning mb-2"></i>
                                <h5 class="card-title">邮件服务</h5>
                                <p class="card-text">
                                    <span class="badge bg-warning" id="emailStatus">模拟模式</span>
                                </p>
                                <small class="text-muted">功能可用</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-brain fa-2x text-success mb-2"></i>
                                <h5 class="card-title">AI服务</h5>
                                <p class="card-text">
                                    <span class="badge bg-success" id="aiStatus">已配置</span>
                                </p>
                                <small class="text-muted">OpenRouter + OCR</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能模块状态 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h3>功能模块状态</h3>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>模块</th>
                                        <th>状态</th>
                                        <th>功能</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><i class="fas fa-file-alt me-2"></i>文档管理</td>
                                        <td><span class="badge bg-success">正常</span></td>
                                        <td>上传、下载、预览、版本控制、分享</td>
                                        <td><a href="/dms/documents" class="btn btn-sm btn-outline-primary">访问</a></td>
                                    </tr>
                                    <tr>
                                        <td><i class="fas fa-users me-2"></i>用户管理</td>
                                        <td><span class="badge bg-success">正常</span></td>
                                        <td>用户CRUD、密码管理、权限控制</td>
                                        <td><a href="/dms/users" class="btn btn-sm btn-outline-primary">访问</a></td>
                                    </tr>
                                    <tr>
                                        <td><i class="fas fa-graduation-cap me-2"></i>培训管理</td>
                                        <td><span class="badge bg-success">正常</span></td>
                                        <td>课程管理、培训分配、进度跟踪</td>
                                        <td><a href="/dms/training-courses" class="btn btn-sm btn-outline-primary">访问</a></td>
                                    </tr>
                                    <tr>
                                        <td><i class="fas fa-clipboard-list me-2"></i>培训分配</td>
                                        <td><span class="badge bg-success">正常</span></td>
                                        <td>任务分配、进度监控、报表生成</td>
                                        <td><a href="/dms/training-assignment" class="btn btn-sm btn-outline-primary">访问</a></td>
                                    </tr>
                                    <tr>
                                        <td><i class="fas fa-chart-bar me-2"></i>仪表板</td>
                                        <td><span class="badge bg-success">正常</span></td>
                                        <td>系统概览、统计图表、快速操作</td>
                                        <td><a href="/dms/dashboard" class="btn btn-sm btn-outline-primary">访问</a></td>
                                    </tr>
                                    <tr>
                                        <td><i class="fas fa-eye me-2"></i>OCR识别</td>
                                        <td><span class="badge bg-info">已配置</span></td>
                                        <td>图像文字识别、PDF处理</td>
                                        <td><button class="btn btn-sm btn-outline-info" onclick="testOCR()">测试</button></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle me-2"></i>系统信息</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr><td><strong>应用版本:</strong></td><td>1.0.0</td></tr>
                                    <tr><td><strong>Spring Boot:</strong></td><td>3.2.0</td></tr>
                                    <tr><td><strong>Java版本:</strong></td><td>17</td></tr>
                                    <tr><td><strong>数据库:</strong></td><td>H2 (内存)</td></tr>
                                    <tr><td><strong>启动时间:</strong></td><td id="startTime">-</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-cogs me-2"></i>配置信息</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr><td><strong>文件上传目录:</strong></td><td>uploads/</td></tr>
                                    <tr><td><strong>最大文件大小:</strong></td><td>50MB</td></tr>
                                    <tr><td><strong>JWT过期时间:</strong></td><td>24小时</td></tr>
                                    <tr><td><strong>OCR语言:</strong></td><td>中文+英文</td></tr>
                                    <tr><td><strong>AI模型:</strong></td><td>DeepSeek R1</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载时设置启动时间
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('startTime').textContent = new Date().toLocaleString('zh-CN');
        });

        function checkAllSystems() {
            // 模拟系统检查
            const statuses = ['appStatus', 'dbStatus', 'emailStatus', 'aiStatus'];
            statuses.forEach(id => {
                const element = document.getElementById(id);
                element.textContent = '检查中...';
                element.className = 'badge bg-warning';
            });

            setTimeout(() => {
                document.getElementById('appStatus').textContent = '运行中';
                document.getElementById('appStatus').className = 'badge bg-success';
                
                document.getElementById('dbStatus').textContent = 'H2内存数据库';
                document.getElementById('dbStatus').className = 'badge bg-success';
                
                document.getElementById('emailStatus').textContent = '模拟模式';
                document.getElementById('emailStatus').className = 'badge bg-warning';
                
                document.getElementById('aiStatus').textContent = '已配置';
                document.getElementById('aiStatus').className = 'badge bg-success';
                
                alert('系统状态检查完成！');
            }, 2000);
        }

        function testOCR() {
            alert('OCR测试功能开发中，请访问文档管理页面使用OCR功能。');
        }
    </script>
</body>
</html>
