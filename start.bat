@echo off
echo ========================================
echo Pharmaceutical DMS - Starting Application
echo ========================================

echo.
echo Checking Java installation...
java -version
if %errorlevel% neq 0 (
    echo ERROR: Java not found. Please ensure Java 17+ is installed and in PATH.
    pause
    exit /b 1
)

echo.
echo Checking Maven installation...
mvn -version
if %errorlevel% neq 0 (
    echo ERROR: Maven not found. Please ensure <PERSON>ven is installed and in PATH.
    pause
    exit /b 1
)

echo.
echo Starting PostgreSQL (if not already running)...
echo Please ensure PostgreSQL is running on localhost:5432

echo.
echo Building and starting the application...
echo Profile: postgresql
echo Port: 8081
echo Context Path: /dms

echo.
echo Starting application...
mvn spring-boot:run "-Dspring-boot.run.profiles=postgresql"

echo.
echo Application stopped.
pause
