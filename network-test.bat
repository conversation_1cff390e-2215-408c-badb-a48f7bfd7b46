@echo off
echo ========================================
echo Pharmaceutical DMS Network Diagnostics
echo ========================================
echo.

echo 1. Checking if application is running...
jps -l | findstr dms
echo.

echo 2. Checking port 8081 status...
netstat -ano | findstr :8081
echo.

echo 3. Testing localhost access...
curl -s -o nul -w "%%{http_code}" http://localhost:8081/dms/api/health/simple
echo.

echo 4. Testing IP access...
curl -s -o nul -w "%%{http_code}" http://**************:8081/dms/api/health/simple
echo.

echo 5. Checking network interfaces...
ipconfig | findstr "IPv4"
echo.

echo 6. Testing ping to local IP...
ping -n 1 **************
echo.

echo 7. Checking firewall status for port 8081...
netsh advfirewall firewall show rule name=all | findstr 8081
echo.

echo ========================================
echo Diagnostics Complete
echo ========================================
pause
