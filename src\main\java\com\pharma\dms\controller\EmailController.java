package com.pharma.dms.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.User;
import com.pharma.dms.security.UserPrincipal;
import com.pharma.dms.service.EmailService;
import com.pharma.dms.service.UserService;

/**
 * 邮件管理控制器
 */
@RestController
@RequestMapping("/api/email")
@CrossOrigin(origins = "*", maxAge = 3600)
public class EmailController {

    @Autowired
    private EmailService emailService;

    @Autowired
    private UserService userService;

    /**
     * 获取邮件配置状态
     */
    @GetMapping("/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getEmailStatus() {
        try {
            Map<String, Object> status = Map.of(
                "enabled", emailService.isEmailEnabled(),
                "fromEmail", emailService.getFromEmail(),
                "timestamp", java.time.LocalDateTime.now().toString()
            );

            return ResponseEntity.ok(ApiResponse.success("邮件状态获取成功", status));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取邮件状态失败: " + e.getMessage(), null));
        }
    }

    /**
     * 测试邮件配置
     */
    @PostMapping("/test")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testEmailConfiguration(
            @RequestBody Map<String, String> request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        try {
            String testEmail = request.get("email");
            if (testEmail == null || testEmail.trim().isEmpty()) {
                // 使用当前用户的邮箱
                User currentUser = userService.getUserByUsername(userPrincipal.getUsername())
                        .orElseThrow(() -> new RuntimeException("用户不存在"));
                testEmail = currentUser.getEmail();
            }

            System.out.println("=== 邮件测试开始 ===");
            System.out.println("测试邮箱: " + testEmail);
            System.out.println("邮件服务状态: " + emailService.isEmailEnabled());

            boolean success = emailService.testEmailConfiguration(testEmail);

            Map<String, Object> result = Map.of(
                "success", success,
                "message", success ? "邮件测试成功" : "邮件测试失败",
                "testEmail", testEmail,
                "emailEnabled", emailService.isEmailEnabled()
            );

            System.out.println("邮件测试结果: " + success);
            System.out.println("=== 邮件测试结束 ===");

            return ResponseEntity.ok(ApiResponse.success("邮件测试完成", result));

        } catch (Exception e) {
            System.out.println("邮件测试失败: " + e.getMessage());
            e.printStackTrace();
            
            Map<String, Object> result = Map.of(
                "success", false,
                "message", "邮件发送失败: " + e.getMessage()
            );

            return ResponseEntity.ok(ApiResponse.success("邮件测试完成", result));
        }
    }

    /**
     * 发送自定义邮件
     */
    @PostMapping("/send")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> sendCustomEmail(
            @RequestBody Map<String, String> request) {
        
        try {
            String to = request.get("to");
            String subject = request.get("subject");
            String content = request.get("content");
            String type = request.getOrDefault("type", "text");

            if (to == null || subject == null || content == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("缺少必要参数", null));
            }

            if ("html".equals(type)) {
                emailService.sendHtmlEmail(to, subject, content);
            } else {
                emailService.sendSimpleEmail(to, subject, content);
            }

            return ResponseEntity.ok(ApiResponse.success("邮件发送成功", "邮件已发送"));

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("邮件发送失败: " + e.getMessage(), null));
        }
    }

    /**
     * 发送系统通知邮件
     */
    @PostMapping("/notification")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> sendSystemNotification(
            @RequestBody Map<String, String> request) {
        
        try {
            String to = request.get("to");
            String subject = request.get("subject");
            String message = request.get("message");

            if (to == null || subject == null || message == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("缺少必要参数", null));
            }

            emailService.sendSystemNotification(to, subject, message);

            return ResponseEntity.ok(ApiResponse.success("系统通知发送成功", "通知已发送"));

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("系统通知发送失败: " + e.getMessage(), null));
        }
    }

    /**
     * 发送欢迎邮件给新用户
     */
    @PostMapping("/welcome/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> sendWelcomeEmail(
            @PathVariable Long userId,
            @RequestBody Map<String, String> request) {
        
        try {
            User user = userService.getUserById(userId)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            String temporaryPassword = request.get("temporaryPassword");
            if (temporaryPassword == null) {
                temporaryPassword = "请联系管理员获取密码";
            }

            emailService.sendWelcomeEmail(user, temporaryPassword);

            return ResponseEntity.ok(ApiResponse.success("欢迎邮件发送成功", "邮件已发送"));

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("欢迎邮件发送失败: " + e.getMessage(), null));
        }
    }

    /**
     * 发送密码重置邮件
     */
    @PostMapping("/password-reset/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> sendPasswordResetEmail(
            @PathVariable Long userId,
            @RequestBody Map<String, String> request) {
        
        try {
            User user = userService.getUserById(userId)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            String newPassword = request.get("newPassword");
            if (newPassword == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("缺少新密码参数", null));
            }

            emailService.sendPasswordResetEmail(user, newPassword);

            return ResponseEntity.ok(ApiResponse.success("密码重置邮件发送成功", "邮件已发送"));

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("密码重置邮件发送失败: " + e.getMessage(), null));
        }
    }
}
