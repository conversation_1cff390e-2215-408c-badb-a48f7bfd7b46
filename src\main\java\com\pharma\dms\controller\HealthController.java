package com.pharma.dms.controller;

import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/health")
@CrossOrigin(origins = "*")
public class HealthController {

    @Autowired
    private DataSource dataSource;

    @GetMapping
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            health.put("status", "UP");
            health.put("timestamp", LocalDateTime.now().toString());
            health.put("application", "Pharmaceutical DMS");
            health.put("version", "1.0.0");
            
            // 检查数据库连接
            try (Connection connection = dataSource.getConnection()) {
                health.put("database", "UP");
                health.put("databaseUrl", connection.getMetaData().getURL());
            } catch (Exception e) {
                health.put("database", "DOWN");
                health.put("databaseError", e.getMessage());
            }
            
            // 检查内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            Map<String, Object> memory = new HashMap<>();
            memory.put("max", maxMemory / 1024 / 1024 + " MB");
            memory.put("total", totalMemory / 1024 / 1024 + " MB");
            memory.put("used", usedMemory / 1024 / 1024 + " MB");
            memory.put("free", freeMemory / 1024 / 1024 + " MB");
            health.put("memory", memory);
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            return ResponseEntity.status(500).body(health);
        }
    }

    @GetMapping("/simple")
    public ResponseEntity<String> simpleHealth() {
        return ResponseEntity.ok("OK - " + LocalDateTime.now());
    }

    @GetMapping("/network")
    public ResponseEntity<Map<String, Object>> networkInfo() {
        Map<String, Object> info = new HashMap<>();
        try {
            info.put("timestamp", LocalDateTime.now().toString());
            info.put("serverAddress", "0.0.0.0");
            info.put("serverPort", "8081");
            info.put("contextPath", "/dms");
            info.put("accessUrls", new String[]{
                "http://localhost:8081/dms",
                "http://127.0.0.1:8081/dms",
                "http://**************:8081/dms"
            });
            info.put("status", "ACCESSIBLE");

            return ResponseEntity.ok(info);
        } catch (Exception e) {
            info.put("status", "ERROR");
            info.put("error", e.getMessage());
            return ResponseEntity.status(500).body(info);
        }
    }

    @GetMapping("/database")
    public ResponseEntity<Map<String, Object>> databaseHealth() {
        Map<String, Object> result = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            result.put("status", "UP");
            result.put("url", connection.getMetaData().getURL());
            result.put("driverName", connection.getMetaData().getDriverName());
            result.put("driverVersion", connection.getMetaData().getDriverVersion());
            result.put("timestamp", LocalDateTime.now().toString());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("status", "DOWN");
            result.put("error", e.getMessage());
            result.put("timestamp", LocalDateTime.now().toString());
            
            return ResponseEntity.status(500).body(result);
        }
    }
}
