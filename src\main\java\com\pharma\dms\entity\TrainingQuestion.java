package com.pharma.dms.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "training_questions")
public class TrainingQuestion extends BaseEntity {

    @NotBlank
    @Size(max = 1000)
    @Column(name = "question_text", nullable = false)
    private String questionText;

    @Enumerated(EnumType.STRING)
    @Column(name = "question_type", nullable = false)
    private QuestionType questionType = QuestionType.MULTIPLE_CHOICE;

    @Column(name = "points", nullable = false)
    private Integer points = 1;

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "explanation")
    private String explanation;

    @Column(name = "reference_material")
    private String referenceMaterial;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "course_id")
    private TrainingCourse course;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "module_id")
    private TrainingModule module;

    @OneToMany(mappedBy = "question", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<TrainingQuestionOption> options = new ArrayList<>();

    @OneToMany(mappedBy = "question", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<TrainingAnswer> answers = new ArrayList<>();

    // Constructors
    public TrainingQuestion() {}

    public TrainingQuestion(String questionText, QuestionType questionType) {
        this.questionText = questionText;
        this.questionType = questionType;
    }

    // Getters and Setters
    public String getQuestionText() {
        return questionText;
    }

    public void setQuestionText(String questionText) {
        this.questionText = questionText;
    }

    public QuestionType getQuestionType() {
        return questionType;
    }

    public void setQuestionType(QuestionType questionType) {
        this.questionType = questionType;
    }

    public Integer getPoints() {
        return points;
    }

    public void setPoints(Integer points) {
        this.points = points;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public String getExplanation() {
        return explanation;
    }

    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }

    public String getReferenceMaterial() {
        return referenceMaterial;
    }

    public void setReferenceMaterial(String referenceMaterial) {
        this.referenceMaterial = referenceMaterial;
    }

    public TrainingCourse getCourse() {
        return course;
    }

    public void setCourse(TrainingCourse course) {
        this.course = course;
    }

    public TrainingModule getModule() {
        return module;
    }

    public void setModule(TrainingModule module) {
        this.module = module;
    }

    public List<TrainingQuestionOption> getOptions() {
        return options;
    }

    public void setOptions(List<TrainingQuestionOption> options) {
        this.options = options;
    }

    public List<TrainingAnswer> getAnswers() {
        return answers;
    }

    public void setAnswers(List<TrainingAnswer> answers) {
        this.answers = answers;
    }

    // Helper methods
    public boolean hasOptions() {
        return options != null && !options.isEmpty();
    }

    public List<TrainingQuestionOption> getCorrectOptions() {
        return options.stream()
                .filter(TrainingQuestionOption::getIsCorrect)
                .toList();
    }

    public boolean isMultipleChoice() {
        return questionType == QuestionType.MULTIPLE_CHOICE;
    }

    public boolean isMultipleSelect() {
        return questionType == QuestionType.MULTIPLE_SELECT;
    }

    public boolean isTrueFalse() {
        return questionType == QuestionType.TRUE_FALSE;
    }

    public boolean isTextInput() {
        return questionType == QuestionType.TEXT_INPUT;
    }

    // Question Type Enum
    public enum QuestionType {
        MULTIPLE_CHOICE("Multiple Choice"),
        MULTIPLE_SELECT("Multiple Select"),
        TRUE_FALSE("True/False"),
        TEXT_INPUT("Text Input"),
        ESSAY("Essay"),
        FILL_IN_BLANK("Fill in the Blank"),
        MATCHING("Matching"),
        ORDERING("Ordering");

        private final String displayName;

        QuestionType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}

// Question Option Entity
@Entity
@Table(name = "training_question_options")
class TrainingQuestionOption extends BaseEntity {

    @NotBlank
    @Size(max = 500)
    @Column(name = "option_text", nullable = false)
    private String optionText;

    @Column(name = "is_correct", nullable = false)
    private Boolean isCorrect = false;

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "question_id", nullable = false)
    private TrainingQuestion question;

    // Constructors
    public TrainingQuestionOption() {}

    public TrainingQuestionOption(String optionText, Boolean isCorrect) {
        this.optionText = optionText;
        this.isCorrect = isCorrect;
    }

    // Getters and Setters
    public String getOptionText() {
        return optionText;
    }

    public void setOptionText(String optionText) {
        this.optionText = optionText;
    }

    public Boolean getIsCorrect() {
        return isCorrect;
    }

    public void setIsCorrect(Boolean isCorrect) {
        this.isCorrect = isCorrect;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public TrainingQuestion getQuestion() {
        return question;
    }

    public void setQuestion(TrainingQuestion question) {
        this.question = question;
    }
}
