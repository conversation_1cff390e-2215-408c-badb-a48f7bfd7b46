package com.pharma.dms.controller;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.Department;
import com.pharma.dms.repository.DepartmentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/departments")
@CrossOrigin(origins = "*", maxAge = 3600)
public class DepartmentController {

    @Autowired
    private DepartmentRepository departmentRepository;

    @GetMapping
    public ResponseEntity<ApiResponse<List<Department>>> getAllDepartments() {
        List<Department> departments = departmentRepository.findAllOrderByName();
        return ResponseEntity.ok(ApiResponse.success("Departments retrieved successfully", departments));
    }

    @GetMapping("/active")
    public ResponseEntity<ApiResponse<List<Department>>> getActiveDepartments() {
        List<Department> departments = departmentRepository.findByIsActiveTrue();
        return ResponseEntity.ok(ApiResponse.success("Active departments retrieved", departments));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Department>> getDepartmentById(@PathVariable Long id) {
        Optional<Department> department = departmentRepository.findById(id);
        
        if (department.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success("Department found", department.get()));
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Department>> createDepartment(@RequestBody Department department) {
        try {
            if (departmentRepository.existsByName(department.getName())) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Department name already exists"));
            }
            
            if (department.getCode() != null && departmentRepository.existsByCode(department.getCode())) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Department code already exists"));
            }
            
            Department savedDepartment = departmentRepository.save(department);
            return ResponseEntity.ok(ApiResponse.success("Department created successfully", savedDepartment));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to create department", e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Department>> updateDepartment(@PathVariable Long id, 
                                                                   @RequestBody Department departmentDetails) {
        try {
            Department department = departmentRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("Department not found"));

            department.setName(departmentDetails.getName());
            department.setDescription(departmentDetails.getDescription());
            department.setCode(departmentDetails.getCode());
            department.setIsActive(departmentDetails.getIsActive());

            Department updatedDepartment = departmentRepository.save(department);
            return ResponseEntity.ok(ApiResponse.success("Department updated successfully", updatedDepartment));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update department", e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteDepartment(@PathVariable Long id) {
        try {
            Department department = departmentRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("Department not found"));

            // Soft delete - just deactivate
            department.setIsActive(false);
            departmentRepository.save(department);
            
            return ResponseEntity.ok(ApiResponse.success("Department deleted successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to delete department", e.getMessage()));
        }
    }
}
