#!/bin/bash

# 制药DMS应用程序启动脚本
# 适用于 Git Bash 环境

echo "=========================================="
echo "🚀 启动制药DMS应用程序"
echo "=========================================="

# 应用程序路径
APP_HOME="/d/learn/java/dms"
JAR_FILE="$APP_HOME/target/dms-1.0.0.jar"

# 检查应用程序目录
if [ ! -d "$APP_HOME" ]; then
    echo "❌ 错误: 应用程序目录未找到 $APP_HOME"
    exit 1
fi

echo "📍 应用程序路径: $APP_HOME"
cd "$APP_HOME"

# 检查Java环境
echo "☕ 检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "❌ 错误: Java未找到，请确认Java已安装并在PATH中"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n 1)
echo "✅ Java版本: $JAVA_VERSION"

# 检查Maven环境
echo "🔧 检查Maven环境..."
if ! command -v mvn &> /dev/null; then
    echo "❌ 错误: Maven未找到，请确认Maven已安装并在PATH中"
    exit 1
fi

MVN_VERSION=$(mvn -version | head -n 1)
echo "✅ Maven版本: $MVN_VERSION"

# 检查应用程序是否已经在运行
echo "🔍 检查应用程序状态..."
if pgrep -f "dms-1.0.0.jar" > /dev/null; then
    echo "⚠️  应用程序已经在运行"
    echo "📋 当前进程:"
    pgrep -f "dms-1.0.0.jar"
    
    read -p "是否要重启应用程序? (y/n): " restart
    if [ "$restart" = "y" ] || [ "$restart" = "Y" ]; then
        echo "🔄 停止现有应用程序..."
        pkill -f "dms-1.0.0.jar"
        sleep 3
    else
        echo "✅ 保持现有应用程序运行"
        exit 0
    fi
fi

# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    echo "📦 JAR文件不存在，开始编译..."
    
    echo "🧹 清理旧的编译文件..."
    mvn clean -q
    
    echo "🔨 编译应用程序..."
    mvn package -DskipTests -q
    
    if [ $? -eq 0 ]; then
        echo "✅ 编译成功"
    else
        echo "❌ 编译失败"
        exit 1
    fi
else
    echo "✅ JAR文件已存在: $JAR_FILE"
fi

# 启动应用程序
echo "🚀 启动应用程序..."
echo "📊 配置信息:"
echo "   端口: 8081"
echo "   上下文路径: /dms"
echo "   数据库: PostgreSQL"
echo "   访问地址: http://**************:8081/dms/login"

# 启动应用程序（后台运行）
nohup java -jar "$JAR_FILE" \
    --spring.profiles.active=postgresql \
    --server.address=0.0.0.0 \
    --server.port=8081 \
    > application.log 2>&1 &

APP_PID=$!
echo "🔄 应用程序启动中... (PID: $APP_PID)"

# 等待应用程序启动
echo "⏳ 等待应用程序启动完成..."
for i in {1..30}; do
    if curl -s http://localhost:8081/dms/api/health/simple > /dev/null 2>&1; then
        echo "✅ 应用程序启动成功!"
        break
    fi
    
    if [ $i -eq 30 ]; then
        echo "⚠️  应用程序启动超时，请检查日志文件: application.log"
        exit 1
    fi
    
    echo "   等待中... ($i/30)"
    sleep 2
done

echo "=========================================="
echo "✅ 应用程序启动完成"
echo "📊 访问信息:"
echo "   本地访问: http://localhost:8081/dms/login"
echo "   网络访问: http://**************:8081/dms/login"
echo "   健康检查: http://localhost:8081/dms/api/health"
echo "   进程ID: $APP_PID"
echo "   日志文件: $APP_HOME/application.log"
echo "=========================================="
