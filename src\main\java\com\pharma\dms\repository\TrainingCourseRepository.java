package com.pharma.dms.repository;

import com.pharma.dms.entity.TrainingCourse;
import com.pharma.dms.entity.Department;
import com.pharma.dms.entity.Role;
import com.pharma.dms.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TrainingCourseRepository extends JpaRepository<TrainingCourse, Long> {

    // Basic queries
    Optional<TrainingCourse> findByCourseCode(String courseCode);
    
    Boolean existsByCourseCode(String courseCode);
    
    List<TrainingCourse> findByStatus(TrainingCourse.CourseStatus status);
    
    List<TrainingCourse> findByCourseType(TrainingCourse.CourseType courseType);
    
    List<TrainingCourse> findByInstructor(User instructor);
    
    List<TrainingCourse> findByIsMandatoryTrue();
    
    List<TrainingCourse> findByAutoAssignTrue();

    // Active and effective courses
    @Query("SELECT c FROM TrainingCourse c WHERE c.status = 'ACTIVE' AND " +
           "(c.effectiveDate IS NULL OR c.effectiveDate <= :now) AND " +
           "(c.expiryDate IS NULL OR c.expiryDate > :now)")
    List<TrainingCourse> findActiveCourses(@Param("now") LocalDateTime now);

    // Search queries
    @Query("SELECT c FROM TrainingCourse c WHERE " +
           "(:title IS NULL OR LOWER(c.title) LIKE LOWER(CONCAT('%', :title, '%'))) AND " +
           "(:courseCode IS NULL OR LOWER(c.courseCode) LIKE LOWER(CONCAT('%', :courseCode, '%'))) AND " +
           "(:courseType IS NULL OR c.courseType = :courseType) AND " +
           "(:status IS NULL OR c.status = :status) AND " +
           "(:instructorId IS NULL OR c.instructor.id = :instructorId) AND " +
           "(:isMandatory IS NULL OR c.isMandatory = :isMandatory)")
    Page<TrainingCourse> findCoursesWithFilters(@Param("title") String title,
                                               @Param("courseCode") String courseCode,
                                               @Param("courseType") TrainingCourse.CourseType courseType,
                                               @Param("status") TrainingCourse.CourseStatus status,
                                               @Param("instructorId") Long instructorId,
                                               @Param("isMandatory") Boolean isMandatory,
                                               Pageable pageable);

    // Department-based queries
    @Query("SELECT c FROM TrainingCourse c JOIN c.targetDepartments d WHERE d.id = :departmentId")
    List<TrainingCourse> findByTargetDepartment(@Param("departmentId") Long departmentId);
    
    @Query("SELECT c FROM TrainingCourse c JOIN c.targetDepartments d WHERE d IN :departments")
    List<TrainingCourse> findByTargetDepartments(@Param("departments") List<Department> departments);

    // Role-based queries
    @Query("SELECT c FROM TrainingCourse c JOIN c.targetRoles r WHERE r.id = :roleId")
    List<TrainingCourse> findByTargetRole(@Param("roleId") Long roleId);
    
    @Query("SELECT c FROM TrainingCourse c JOIN c.targetRoles r WHERE r IN :roles")
    List<TrainingCourse> findByTargetRoles(@Param("roles") List<Role> roles);

    // User-specific queries
    @Query("SELECT c FROM TrainingCourse c WHERE " +
           "(c.targetDepartments IS EMPTY OR :userDepartment MEMBER OF c.targetDepartments) AND " +
           "(c.targetRoles IS EMPTY OR EXISTS (SELECT r FROM c.targetRoles r WHERE r IN :userRoles)) AND " +
           "c.status = 'ACTIVE' AND " +
           "(c.effectiveDate IS NULL OR c.effectiveDate <= :now) AND " +
           "(c.expiryDate IS NULL OR c.expiryDate > :now)")
    List<TrainingCourse> findAvailableCoursesForUser(@Param("userDepartment") Department userDepartment,
                                                    @Param("userRoles") List<Role> userRoles,
                                                    @Param("now") LocalDateTime now);

    // Expiry and reminder queries
    @Query("SELECT c FROM TrainingCourse c WHERE c.expiryDate BETWEEN :startDate AND :endDate")
    List<TrainingCourse> findCoursesExpiringBetween(@Param("startDate") LocalDateTime startDate,
                                                   @Param("endDate") LocalDateTime endDate);

    @Query("SELECT c FROM TrainingCourse c WHERE c.expiryDate < :date")
    List<TrainingCourse> findExpiredCourses(@Param("date") LocalDateTime date);

    // Statistics queries
    @Query("SELECT COUNT(c) FROM TrainingCourse c WHERE c.status = :status")
    Long countByStatus(@Param("status") TrainingCourse.CourseStatus status);
    
    @Query("SELECT COUNT(c) FROM TrainingCourse c WHERE c.courseType = :courseType")
    Long countByCourseType(@Param("courseType") TrainingCourse.CourseType courseType);
    
    @Query("SELECT COUNT(c) FROM TrainingCourse c WHERE c.instructor.id = :instructorId")
    Long countByInstructor(@Param("instructorId") Long instructorId);
    
    @Query("SELECT COUNT(c) FROM TrainingCourse c WHERE c.isMandatory = true")
    Long countMandatoryCourses();

    // Recent courses
    @Query("SELECT c FROM TrainingCourse c WHERE c.createdAt >= :since ORDER BY c.createdAt DESC")
    List<TrainingCourse> findRecentlyCreated(@Param("since") LocalDateTime since, Pageable pageable);
    
    @Query("SELECT c FROM TrainingCourse c WHERE c.updatedAt >= :since ORDER BY c.updatedAt DESC")
    List<TrainingCourse> findRecentlyUpdated(@Param("since") LocalDateTime since, Pageable pageable);

    // Popular courses (by enrollment count)
    @Query("SELECT c FROM TrainingCourse c ORDER BY SIZE(c.assignments) DESC")
    List<TrainingCourse> findMostPopularCourses(Pageable pageable);

    // Courses requiring approval
    @Query("SELECT c FROM TrainingCourse c WHERE c.status = 'UNDER_REVIEW' ORDER BY c.createdAt ASC")
    List<TrainingCourse> findCoursesAwaitingApproval();

    // Auto-assignment eligible courses
    @Query("SELECT c FROM TrainingCourse c WHERE c.autoAssign = true AND c.status = 'ACTIVE' AND " +
           "(c.effectiveDate IS NULL OR c.effectiveDate <= :now) AND " +
           "(c.expiryDate IS NULL OR c.expiryDate > :now)")
    List<TrainingCourse> findAutoAssignEligibleCourses(@Param("now") LocalDateTime now);

    // Courses by related document
    @Query("SELECT c FROM TrainingCourse c WHERE c.relatedDocument.id = :documentId")
    List<TrainingCourse> findByRelatedDocument(@Param("documentId") Long documentId);
}
