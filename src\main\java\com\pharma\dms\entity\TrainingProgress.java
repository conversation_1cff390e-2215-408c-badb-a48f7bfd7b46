package com.pharma.dms.entity;

import jakarta.persistence.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "training_progress")
public class TrainingProgress extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "training_record_id", nullable = false)
    private TrainingRecord trainingRecord;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "module_id")
    private TrainingModule module;

    @Column(name = "progress_percentage", nullable = false)
    private Integer progressPercentage = 0;

    @Column(name = "time_spent_minutes", nullable = false)
    private Integer timeSpentMinutes = 0;

    @Column(name = "last_accessed")
    private LocalDateTime lastAccessed;

    @Column(name = "is_completed", nullable = false)
    private Boolean isCompleted = false;

    @Column(name = "completion_date")
    private LocalDateTime completionDate;

    @Column(name = "bookmark_position")
    private String bookmarkPosition;

    @Column(name = "notes")
    private String notes;

    // Constructors
    public TrainingProgress() {}

    public TrainingProgress(TrainingRecord trainingRecord, TrainingModule module) {
        this.trainingRecord = trainingRecord;
        this.module = module;
        this.lastAccessed = LocalDateTime.now();
    }

    // Getters and Setters
    public TrainingRecord getTrainingRecord() {
        return trainingRecord;
    }

    public void setTrainingRecord(TrainingRecord trainingRecord) {
        this.trainingRecord = trainingRecord;
    }

    public TrainingModule getModule() {
        return module;
    }

    public void setModule(TrainingModule module) {
        this.module = module;
    }

    public Integer getProgressPercentage() {
        return progressPercentage;
    }

    public void setProgressPercentage(Integer progressPercentage) {
        this.progressPercentage = progressPercentage;
    }

    public Integer getTimeSpentMinutes() {
        return timeSpentMinutes;
    }

    public void setTimeSpentMinutes(Integer timeSpentMinutes) {
        this.timeSpentMinutes = timeSpentMinutes;
    }

    public LocalDateTime getLastAccessed() {
        return lastAccessed;
    }

    public void setLastAccessed(LocalDateTime lastAccessed) {
        this.lastAccessed = lastAccessed;
    }

    public Boolean getIsCompleted() {
        return isCompleted;
    }

    public void setIsCompleted(Boolean isCompleted) {
        this.isCompleted = isCompleted;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public String getBookmarkPosition() {
        return bookmarkPosition;
    }

    public void setBookmarkPosition(String bookmarkPosition) {
        this.bookmarkPosition = bookmarkPosition;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    // Helper methods
    public void updateProgress(Integer percentage) {
        this.progressPercentage = percentage;
        this.lastAccessed = LocalDateTime.now();
        
        if (percentage >= 100 && !isCompleted) {
            this.isCompleted = true;
            this.completionDate = LocalDateTime.now();
        }
    }

    public void addTimeSpent(Integer minutes) {
        this.timeSpentMinutes += minutes;
        this.lastAccessed = LocalDateTime.now();
    }

    public void markCompleted() {
        this.isCompleted = true;
        this.progressPercentage = 100;
        this.completionDate = LocalDateTime.now();
        this.lastAccessed = LocalDateTime.now();
    }
}
