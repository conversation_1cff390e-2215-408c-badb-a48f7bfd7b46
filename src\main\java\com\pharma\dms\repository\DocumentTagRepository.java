package com.pharma.dms.repository;

import com.pharma.dms.entity.DocumentTag;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentTagRepository extends JpaRepository<DocumentTag, Long> {

    Optional<DocumentTag> findByName(String name);
    
    Boolean existsByName(String name);
    
    List<DocumentTag> findByIsActiveTrue();
    
    List<DocumentTag> findByIsActiveFalse();
    
    @Query("SELECT t FROM DocumentTag t ORDER BY t.usageCount DESC")
    List<DocumentTag> findAllOrderByUsageCount();
    
    @Query("SELECT t FROM DocumentTag t WHERE t.isActive = true ORDER BY t.usageCount DESC")
    List<DocumentTag> findActiveOrderByUsageCount();
    
    @Query("SELECT t FROM DocumentTag t WHERE LOWER(t.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<DocumentTag> findByNameContainingIgnoreCase(@Param("name") String name);
    
    @Query("SELECT t FROM DocumentTag t WHERE t.usageCount > 0 ORDER BY t.usageCount DESC")
    List<DocumentTag> findUsedTags();
    
    @Query("SELECT t FROM DocumentTag t WHERE t.usageCount = 0")
    List<DocumentTag> findUnusedTags();
}
