package com.pharma.dms.entity;

import jakarta.persistence.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "training_records")
public class TrainingRecord extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "course_id", nullable = false)
    private TrainingCourse course;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assignment_id")
    private TrainingAssignment assignment;

    @Column(name = "start_date", nullable = false)
    private LocalDateTime startDate;

    @Column(name = "completion_date")
    private LocalDateTime completionDate;

    @Column(name = "certificate_issue_date")
    private LocalDateTime certificateIssueDate;

    @Column(name = "certificate_expiry_date")
    private LocalDateTime certificateExpiryDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TrainingStatus status = TrainingStatus.NOT_STARTED;

    @Column(name = "final_score")
    private Integer finalScore;

    @Column(name = "passing_score", nullable = false)
    private Integer passingScore = 80;

    @Column(name = "attempts_count", nullable = false)
    private Integer attemptsCount = 0;

    @Column(name = "max_attempts", nullable = false)
    private Integer maxAttempts = 3;

    @Column(name = "total_time_minutes")
    private Integer totalTimeMinutes;

    @Column(name = "progress_percentage", nullable = false)
    private Integer progressPercentage = 0;

    // GMP Compliance - Electronic Signature
    @Column(name = "electronic_signature")
    private String electronicSignature;

    @Column(name = "signature_date")
    private LocalDateTime signatureDate;

    @Column(name = "signature_hash")
    private String signatureHash;

    @Column(name = "signature_ip_address")
    private String signatureIpAddress;

    @Column(name = "signature_user_agent")
    private String signatureUserAgent;

    // Certificate Information
    @Column(name = "certificate_number", unique = true)
    private String certificateNumber;

    @Column(name = "certificate_path")
    private String certificatePath;

    @Column(name = "certificate_hash")
    private String certificateHash;

    // Instructor and Approver
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "instructor_id")
    private User instructor;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approved_by")
    private User approvedBy;

    @Column(name = "approval_date")
    private LocalDateTime approvalDate;

    @Column(name = "comments")
    private String comments;

    @Column(name = "feedback")
    private String feedback;

    @Column(name = "is_retrain_required", nullable = false)
    private Boolean isRetrainRequired = false;

    @Column(name = "retrain_reason")
    private String retrainReason;

    @Column(name = "retrain_due_date")
    private LocalDateTime retrainDueDate;

    @OneToMany(mappedBy = "trainingRecord", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<TrainingAttempt> attempts = new ArrayList<>();

    @OneToMany(mappedBy = "trainingRecord", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<TrainingProgress> progressRecords = new ArrayList<>();

    // Constructors
    public TrainingRecord() {
        this.startDate = LocalDateTime.now();
    }

    public TrainingRecord(TrainingCourse course, User user) {
        this();
        this.course = course;
        this.user = user;
        this.passingScore = course.getPassingScore();
        this.maxAttempts = course.getMaxAttempts();
    }

    // Getters and Setters
    public TrainingCourse getCourse() {
        return course;
    }

    public void setCourse(TrainingCourse course) {
        this.course = course;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public TrainingAssignment getAssignment() {
        return assignment;
    }

    public void setAssignment(TrainingAssignment assignment) {
        this.assignment = assignment;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public LocalDateTime getCertificateIssueDate() {
        return certificateIssueDate;
    }

    public void setCertificateIssueDate(LocalDateTime certificateIssueDate) {
        this.certificateIssueDate = certificateIssueDate;
    }

    public LocalDateTime getCertificateExpiryDate() {
        return certificateExpiryDate;
    }

    public void setCertificateExpiryDate(LocalDateTime certificateExpiryDate) {
        this.certificateExpiryDate = certificateExpiryDate;
    }

    public TrainingStatus getStatus() {
        return status;
    }

    public void setStatus(TrainingStatus status) {
        this.status = status;
    }

    public Integer getFinalScore() {
        return finalScore;
    }

    public void setFinalScore(Integer finalScore) {
        this.finalScore = finalScore;
    }

    public Integer getPassingScore() {
        return passingScore;
    }

    public void setPassingScore(Integer passingScore) {
        this.passingScore = passingScore;
    }

    public Integer getAttemptsCount() {
        return attemptsCount;
    }

    public void setAttemptsCount(Integer attemptsCount) {
        this.attemptsCount = attemptsCount;
    }

    public Integer getMaxAttempts() {
        return maxAttempts;
    }

    public void setMaxAttempts(Integer maxAttempts) {
        this.maxAttempts = maxAttempts;
    }

    public Integer getTotalTimeMinutes() {
        return totalTimeMinutes;
    }

    public void setTotalTimeMinutes(Integer totalTimeMinutes) {
        this.totalTimeMinutes = totalTimeMinutes;
    }

    public Integer getProgressPercentage() {
        return progressPercentage;
    }

    public void setProgressPercentage(Integer progressPercentage) {
        this.progressPercentage = progressPercentage;
    }

    public String getElectronicSignature() {
        return electronicSignature;
    }

    public void setElectronicSignature(String electronicSignature) {
        this.electronicSignature = electronicSignature;
    }

    public LocalDateTime getSignatureDate() {
        return signatureDate;
    }

    public void setSignatureDate(LocalDateTime signatureDate) {
        this.signatureDate = signatureDate;
    }

    public String getSignatureHash() {
        return signatureHash;
    }

    public void setSignatureHash(String signatureHash) {
        this.signatureHash = signatureHash;
    }

    public String getSignatureIpAddress() {
        return signatureIpAddress;
    }

    public void setSignatureIpAddress(String signatureIpAddress) {
        this.signatureIpAddress = signatureIpAddress;
    }

    public String getSignatureUserAgent() {
        return signatureUserAgent;
    }

    public void setSignatureUserAgent(String signatureUserAgent) {
        this.signatureUserAgent = signatureUserAgent;
    }

    public String getCertificateNumber() {
        return certificateNumber;
    }

    public void setCertificateNumber(String certificateNumber) {
        this.certificateNumber = certificateNumber;
    }

    public String getCertificatePath() {
        return certificatePath;
    }

    public void setCertificatePath(String certificatePath) {
        this.certificatePath = certificatePath;
    }

    public String getCertificateHash() {
        return certificateHash;
    }

    public void setCertificateHash(String certificateHash) {
        this.certificateHash = certificateHash;
    }

    public User getInstructor() {
        return instructor;
    }

    public void setInstructor(User instructor) {
        this.instructor = instructor;
    }

    public User getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(User approvedBy) {
        this.approvedBy = approvedBy;
    }

    public LocalDateTime getApprovalDate() {
        return approvalDate;
    }

    public void setApprovalDate(LocalDateTime approvalDate) {
        this.approvalDate = approvalDate;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getFeedback() {
        return feedback;
    }

    public void setFeedback(String feedback) {
        this.feedback = feedback;
    }

    public Boolean getIsRetrainRequired() {
        return isRetrainRequired;
    }

    public void setIsRetrainRequired(Boolean isRetrainRequired) {
        this.isRetrainRequired = isRetrainRequired;
    }

    public String getRetrainReason() {
        return retrainReason;
    }

    public void setRetrainReason(String retrainReason) {
        this.retrainReason = retrainReason;
    }

    public LocalDateTime getRetrainDueDate() {
        return retrainDueDate;
    }

    public void setRetrainDueDate(LocalDateTime retrainDueDate) {
        this.retrainDueDate = retrainDueDate;
    }

    public List<TrainingAttempt> getAttempts() {
        return attempts;
    }

    public void setAttempts(List<TrainingAttempt> attempts) {
        this.attempts = attempts;
    }

    public List<TrainingProgress> getProgressRecords() {
        return progressRecords;
    }

    public void setProgressRecords(List<TrainingProgress> progressRecords) {
        this.progressRecords = progressRecords;
    }

    // Helper methods
    public boolean isPassed() {
        return finalScore != null && finalScore >= passingScore;
    }

    public boolean isCompleted() {
        return status == TrainingStatus.COMPLETED;
    }

    public boolean isCertified() {
        return status == TrainingStatus.CERTIFIED;
    }

    public boolean isCertificateExpired() {
        return certificateExpiryDate != null && LocalDateTime.now().isAfter(certificateExpiryDate);
    }

    public boolean isCertificateExpiringSoon() {
        if (certificateExpiryDate == null) return false;
        return LocalDateTime.now().plusDays(30).isAfter(certificateExpiryDate);
    }

    public boolean canRetake() {
        return attemptsCount < maxAttempts;
    }

    public boolean hasElectronicSignature() {
        return electronicSignature != null && !electronicSignature.trim().isEmpty();
    }

    public void incrementAttempts() {
        this.attemptsCount++;
    }

    // Training Status Enum
    public enum TrainingStatus {
        NOT_STARTED("Not Started"),
        IN_PROGRESS("In Progress"),
        COMPLETED("Completed"),
        FAILED("Failed"),
        CERTIFIED("Certified"),
        EXPIRED("Expired"),
        RETRAIN_REQUIRED("Retrain Required");

        private final String displayName;

        TrainingStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}
