package com.pharma.dms.entity;

import jakarta.persistence.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "document_permissions", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"document_id", "user_id"}))
public class DocumentPermission extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "document_id", nullable = false)
    private Document document;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(name = "permission_type", nullable = false)
    private PermissionType permissionType;

    @Column(name = "granted_at", nullable = false)
    private LocalDateTime grantedAt;

    @Column(name = "expires_at")
    private LocalDateTime expiresAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "granted_by")
    private User grantedBy;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    // Constructors
    public DocumentPermission() {
        this.grantedAt = LocalDateTime.now();
    }

    public DocumentPermission(Document document, User user, PermissionType permissionType) {
        this();
        this.document = document;
        this.user = user;
        this.permissionType = permissionType;
    }

    public DocumentPermission(Document document, User user, PermissionType permissionType, User grantedBy) {
        this(document, user, permissionType);
        this.grantedBy = grantedBy;
    }

    // Getters and Setters
    public Document getDocument() {
        return document;
    }

    public void setDocument(Document document) {
        this.document = document;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public PermissionType getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(PermissionType permissionType) {
        this.permissionType = permissionType;
    }

    public LocalDateTime getGrantedAt() {
        return grantedAt;
    }

    public void setGrantedAt(LocalDateTime grantedAt) {
        this.grantedAt = grantedAt;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    public User getGrantedBy() {
        return grantedBy;
    }

    public void setGrantedBy(User grantedBy) {
        this.grantedBy = grantedBy;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    // Helper methods
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    public boolean isValid() {
        return isActive && !isExpired();
    }

    // Permission Type Enum
    public enum PermissionType {
        READ("Read"),
        WRITE("Write"),
        DELETE("Delete"),
        APPROVE("Approve"),
        ADMIN("Admin");

        private final String displayName;

        PermissionType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }

        public boolean includes(PermissionType other) {
            switch (this) {
                case ADMIN:
                    return true;
                case APPROVE:
                    return other == READ || other == WRITE || other == APPROVE;
                case DELETE:
                    return other == READ || other == WRITE || other == DELETE;
                case WRITE:
                    return other == READ || other == WRITE;
                case READ:
                    return other == READ;
                default:
                    return false;
            }
        }
    }
}
