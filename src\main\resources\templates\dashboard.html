<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Pharmaceutical DMS</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" style="min-height: 100vh; background-color: #f8f9fa; border-right: 1px solid #dee2e6;">
                <div class="position-sticky pt-3">
                    <div class="navbar-brand mb-4" style="font-weight: bold; color: #0d6efd !important;">
                        <i class="fas fa-pills me-2"></i>
                        制药文档管理系统
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/dashboard" style="background-color: #0d6efd; color: white !important; border-radius: 0.375rem;">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/users" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/documents" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-file-alt me-2"></i>
                                文档管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/system-overview" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-line me-2"></i>
                                系统概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/my-training" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user-graduate me-2"></i>
                                我的培训
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/training-courses" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-graduation-cap me-2"></i>
                                培训课程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-bar me-2"></i>
                                报表管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-cog me-2"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>

                    <hr>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/profile" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user me-2"></i>
                                个人资料
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">仪表板</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-bell"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Page content -->
                <div>
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">欢迎使用制药文档管理系统</h5>
                        <p class="card-text">您的全面文档管理解决方案，符合GMP合规要求。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    文档总数
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalDocuments">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    活跃用户
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeUsers">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    待审核
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="pendingReviews">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    系统警告
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="systemAlerts">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity and Quick Actions -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">最近活动</h6>
                    </div>
                    <div class="card-body">
                        <div id="recentActivity">
                            <div class="d-flex align-items-center mb-3">
                                <div class="mr-3">
                                    <div class="icon-circle bg-primary">
                                        <i class="fas fa-file-upload text-white"></i>
                                    </div>
                                </div>
                                <div>
                                    <div class="small text-gray-500">December 12, 2024</div>
                                    <span class="font-weight-bold">文档已上传: SOP-001-Rev3.pdf</span>
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <div class="mr-3">
                                    <div class="icon-circle bg-success">
                                        <i class="fas fa-check text-white"></i>
                                    </div>
                                </div>
                                <div>
                                    <div class="small text-gray-500">2024年12月11日</div>
                                    <span class="font-weight-bold">文档已批准: 质量手册 v2.1</span>
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <div class="mr-3">
                                    <div class="icon-circle bg-warning">
                                        <i class="fas fa-user-plus text-white"></i>
                                    </div>
                                </div>
                                <div>
                                    <div class="small text-gray-500">2024年12月10日</div>
                                    <span class="font-weight-bold">新用户注册: 张三</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" type="button" onclick="navigateToDocuments()">
                                <i class="fas fa-upload me-2"></i>
                                上传文档
                            </button>
                            <button class="btn btn-success" type="button" onclick="navigateToUsers()">
                                <i class="fas fa-user-plus me-2"></i>
                                添加用户
                            </button>
                            <button class="btn btn-info" type="button" onclick="navigateToReports()">
                                <i class="fas fa-chart-bar me-2"></i>
                                报表管理
                            </button>
                            <button class="btn btn-warning" type="button" onclick="navigateToSettings()">
                                <i class="fas fa-cog me-2"></i>
                                系统设置
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">系统状态</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>数据库</span>
                                <span class="badge bg-success">在线</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>文件存储</span>
                                <span class="badge bg-success">在线</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>备份服务</span>
                                <span class="badge bg-warning">已计划</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .border-left-primary {
            border-left: 0.25rem solid #4e73df !important;
        }
        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }
        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }
        .border-left-warning {
            border-left: 0.25rem solid #f6c23e !important;
        }
        .icon-circle {
            height: 2.5rem;
            width: 2.5rem;
            border-radius: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .text-gray-500 {
            color: #858796 !important;
        }
        .text-gray-800 {
            color: #5a5c69 !important;
        }
    </style>

    <script>
        // Check authentication before loading dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
        });

        function checkAuthentication() {
            const token = localStorage.getItem('token');
            if (!token) {
                // No token, redirect to login
                window.location.href = '/dms/login';
                return;
            }

            // Token exists, load dashboard data
            loadDashboardData();
            loadRecentActivity();
        }

        async function loadDashboardData() {
            try {
                // Load dashboard statistics
                const response = await fetch('/dms/api/dashboard/stats', {
                    headers: {
                        'Authorization': 'Bearer ' + localStorage.getItem('token')
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    const stats = result.data;

                    // 暂时显示为0，等待有实际数据后再显示
                    document.getElementById('totalDocuments').textContent = '0';
                    document.getElementById('activeUsers').textContent = '0';
                    document.getElementById('pendingReviews').textContent = '0';
                    document.getElementById('systemAlerts').textContent = '0';
                } else {
                    console.error('Failed to load dashboard stats');
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                // 显示为0，等待有实际数据
                document.getElementById('totalDocuments').textContent = '0';
                document.getElementById('activeUsers').textContent = '0';
                document.getElementById('pendingReviews').textContent = '0';
                document.getElementById('systemAlerts').textContent = '0';
            }
        }

        async function loadRecentActivity() {
            try {
                const response = await fetch('/dms/api/dashboard/recent-activity', {
                    headers: {
                        'Authorization': 'Bearer ' + localStorage.getItem('token')
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    updateRecentActivity(result.data.recentLogs);
                }
            } catch (error) {
                console.error('Error loading recent activity:', error);
            }
        }

        function updateRecentActivity(logs) {
            const container = document.getElementById('recentActivity');
            if (logs && logs.length > 0) {
                container.innerHTML = '';
                logs.slice(0, 5).forEach(log => {
                    const activityItem = document.createElement('div');
                    activityItem.className = 'd-flex align-items-center mb-3';
                    activityItem.innerHTML = `
                        <div class="mr-3">
                            <div class="icon-circle ${getActivityIconClass(log.action)}">
                                <i class="${getActivityIcon(log.action)} text-white"></i>
                            </div>
                        </div>
                        <div>
                            <div class="small text-gray-500">${formatDate(log.timestamp)}</div>
                            <span class="font-weight-bold">${log.details || log.action}</span>
                        </div>
                    `;
                    container.appendChild(activityItem);
                });
            }
        }

        function getActivityIconClass(action) {
            switch (action) {
                case 'DOCUMENT_UPLOADED': return 'bg-primary';
                case 'USER_CREATED': return 'bg-success';
                case 'LOGIN_SUCCESS': return 'bg-info';
                case 'DOCUMENT_APPROVED': return 'bg-success';
                default: return 'bg-secondary';
            }
        }

        function getActivityIcon(action) {
            switch (action) {
                case 'DOCUMENT_UPLOADED': return 'fas fa-file-upload';
                case 'USER_CREATED': return 'fas fa-user-plus';
                case 'LOGIN_SUCCESS': return 'fas fa-sign-in-alt';
                case 'DOCUMENT_APPROVED': return 'fas fa-check';
                default: return 'fas fa-info';
            }
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleString();
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }

        // Quick action functions
        function navigateToDocuments() {
            window.location.href = '/dms/documents';
        }

        function navigateToUsers() {
            window.location.href = '/dms/users';
        }

        function navigateToSystemOverview() {
            window.location.href = '/dms/system-overview';
        }

        function navigateToReports() {
            window.location.href = '/dms/reports';
        }

        function navigateToSettings() {
            window.location.href = '/dms/settings';
        }
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
