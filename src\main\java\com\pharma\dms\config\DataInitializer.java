package com.pharma.dms.config;

import java.util.HashSet;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import com.pharma.dms.entity.Department;
import com.pharma.dms.entity.Document;
import com.pharma.dms.entity.DocumentCategory;
import com.pharma.dms.entity.DocumentTag;
import com.pharma.dms.entity.Role;
import com.pharma.dms.entity.User;
import com.pharma.dms.repository.DepartmentRepository;
import com.pharma.dms.repository.DocumentCategoryRepository;
import com.pharma.dms.repository.DocumentRepository;
import com.pharma.dms.repository.DocumentTagRepository;
import com.pharma.dms.repository.RoleRepository;
import com.pharma.dms.repository.UserRepository;

@Component
public class DataInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DocumentCategoryRepository documentCategoryRepository;

    @Autowired
    private DocumentTagRepository documentTagRepository;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        try {
            System.out.println("=== 开始数据初始化 ===");
            initializeRoles();
            System.out.println("角色初始化完成");

            initializeDepartments();
            System.out.println("部门初始化完成");

            initializeDocumentCategories();
            System.out.println("文档分类初始化完成");

            initializeDocumentTags();
            System.out.println("文档标签初始化完成");

            initializeDefaultAdmin();
            System.out.println("默认管理员初始化完成");

            // 创建示例数据
            initializeSampleData();
            System.out.println("=== 数据初始化完成 ===");
        } catch (Exception e) {
            System.out.println("数据初始化失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    private void initializeRoles() {
        for (Role.RoleName roleName : Role.RoleName.values()) {
            if (!roleRepository.existsByName(roleName)) {
                Role role = new Role(roleName, roleName.getDescription());
                roleRepository.save(role);
                logger.info("Created role: {}", roleName);
            }
        }
    }

    private void initializeDepartments() {
        String[][] departments = {
            {"Quality Assurance", "QA", "Quality control and assurance department"},
            {"Research & Development", "RD", "Research and development department"},
            {"Manufacturing", "MFG", "Manufacturing and production department"},
            {"Regulatory Affairs", "RA", "Regulatory compliance department"},
            {"Information Technology", "IT", "IT support and development department"},
            {"Human Resources", "HR", "Human resources department"},
            {"Finance", "FIN", "Finance and accounting department"}
        };

        for (String[] deptData : departments) {
            if (!departmentRepository.existsByCode(deptData[1])) {
                Department department = new Department(deptData[0], deptData[1], deptData[2]);
                departmentRepository.save(department);
                logger.info("Created department: {}", deptData[0]);
            }
        }
    }

    private void initializeDocumentCategories() {
        String[][] categories = {
            {"Standard Operating Procedures", "SOP", "Standard operating procedures and work instructions", "#007bff", "fas fa-clipboard-list"},
            {"Quality Manuals", "QM", "Quality management system documentation", "#28a745", "fas fa-book"},
            {"Validation Documents", "VAL", "Validation protocols and reports", "#ffc107", "fas fa-check-circle"},
            {"Training Materials", "TRN", "Training documents and materials", "#17a2b8", "fas fa-graduation-cap"},
            {"Regulatory Submissions", "REG", "Regulatory filing documents", "#dc3545", "fas fa-file-contract"},
            {"Technical Documents", "TECH", "Technical specifications and drawings", "#6f42c1", "fas fa-cogs"},
            {"Forms and Templates", "FORM", "Standard forms and document templates", "#fd7e14", "fas fa-file-alt"},
            {"Policies and Procedures", "POL", "Company policies and procedures", "#20c997", "fas fa-gavel"}
        };

        for (String[] catData : categories) {
            if (!documentCategoryRepository.existsByCode(catData[1])) {
                DocumentCategory category = new DocumentCategory(catData[0], catData[1], catData[2]);
                category.setColor(catData[3]);
                category.setIcon(catData[4]);
                category.setRequiresApproval(true);
                category.setMaxFileSizeMb(50);
                category.setRetentionPeriodMonths(84); // 7 years
                documentCategoryRepository.save(category);
                logger.info("Created document category: {}", catData[0]);
            }
        }
    }

    private void initializeDocumentTags() {
        String[][] tags = {
            {"GMP", "Good Manufacturing Practice", "#007bff"},
            {"FDA", "FDA Related", "#dc3545"},
            {"ISO", "ISO Standards", "#28a745"},
            {"Critical", "Critical Document", "#dc3545"},
            {"Draft", "Draft Document", "#6c757d"},
            {"Approved", "Approved Document", "#28a745"},
            {"Under Review", "Under Review", "#ffc107"},
            {"Obsolete", "Obsolete Document", "#6c757d"},
            {"Confidential", "Confidential", "#dc3545"},
            {"Public", "Public Document", "#17a2b8"}
        };

        for (String[] tagData : tags) {
            if (!documentTagRepository.existsByName(tagData[0])) {
                DocumentTag tag = new DocumentTag(tagData[0], tagData[1], tagData[2]);
                documentTagRepository.save(tag);
                logger.info("Created document tag: {}", tagData[0]);
            }
        }
    }

    private void initializeDefaultAdmin() {
        if (!userRepository.existsByUsername("admin")) {
            User admin = new User();
            admin.setUsername("admin");
            admin.setEmail("<EMAIL>");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setFirstName("System");
            admin.setLastName("Administrator");
            admin.setIsActive(true);
            admin.setPasswordChangeRequired(true);

            // Set IT department
            Department itDept = departmentRepository.findByCode("IT").orElse(null);
            if (itDept != null) {
                admin.setDepartment(itDept);
            }

            // Set admin role and user role (admin should have all permissions)
            Role adminRole = roleRepository.findByName(Role.RoleName.ROLE_ADMIN).orElse(null);
            Role userRole = roleRepository.findByName(Role.RoleName.ROLE_USER).orElse(null);

            Set<Role> adminRoles = new HashSet<>();
            if (adminRole != null) {
                adminRoles.add(adminRole);
            }
            if (userRole != null) {
                adminRoles.add(userRole);
            }

            if (!adminRoles.isEmpty()) {
                admin.setRoles(adminRoles);
            }

            userRepository.save(admin);
            logger.info("Created default admin user: admin/admin123");
        }

        // Create default QA user
        if (!userRepository.existsByUsername("qa_user")) {
            User qaUser = new User();
            qaUser.setUsername("qa_user");
            qaUser.setEmail("<EMAIL>");
            qaUser.setPassword(passwordEncoder.encode("qa123"));
            qaUser.setFirstName("QA");
            qaUser.setLastName("User");
            qaUser.setIsActive(true);

            // Set QA department
            Department qaDept = departmentRepository.findByCode("QA").orElse(null);
            if (qaDept != null) {
                qaUser.setDepartment(qaDept);
            }

            // Set QA role
            Role qaRole = roleRepository.findByName(Role.RoleName.ROLE_QA).orElse(null);
            if (qaRole != null) {
                qaUser.setRoles(Set.of(qaRole));
            }

            userRepository.save(qaUser);
            logger.info("Created default QA user: qa_user/qa123");
        }

        // Create default regular user
        if (!userRepository.existsByUsername("user")) {
            User user = new User();
            user.setUsername("user");
            user.setEmail("<EMAIL>");
            user.setPassword(passwordEncoder.encode("user123"));
            user.setFirstName("Regular");
            user.setLastName("User");
            user.setIsActive(true);

            // Set RD department
            Department rdDept = departmentRepository.findByCode("RD").orElse(null);
            if (rdDept != null) {
                user.setDepartment(rdDept);
            }

            // Set user role
            Role userRole = roleRepository.findByName(Role.RoleName.ROLE_USER).orElse(null);
            if (userRole != null) {
                user.setRoles(Set.of(userRole));
            }

            userRepository.save(user);
            logger.info("Created default regular user: user/user123");
        }
    }

    private void initializeSampleData() {
        // 只在没有文档时创建示例数据
        if (documentRepository.count() == 0) {
            createSampleDocuments();
        }
    }

    private void createSampleDocuments() {
        try {
            System.out.println("=== 创建示例文档开始 ===");

            User admin = userRepository.findByUsername("admin").orElse(null);
            if (admin == null) {
                System.out.println("未找到admin用户，跳过示例文档创建");
                return;
            }
            System.out.println("找到admin用户: " + admin.getUsername());

            // 获取文档分类
            DocumentCategory sopCategory = documentCategoryRepository.findByCode("SOP").orElse(null);
            System.out.println("SOP分类: " + (sopCategory != null ? sopCategory.getName() : "未找到"));

            // 只创建一个简单的示例文档
            System.out.println("创建示例文档...");
            createSampleDocument("GMP质量管理手册", "制药企业GMP质量管理体系文档",
                                sopCategory, admin, Document.DocumentStatus.PUBLISHED, Document.DocumentClassification.PUBLIC);

            System.out.println("示例文档创建完成");
            logger.info("Created sample documents");
        } catch (Exception e) {
            System.out.println("创建示例文档失败: " + e.getMessage());
            e.printStackTrace();
            logger.error("Error creating sample documents: {}", e.getMessage());
        }
    }

    private void createSampleDocument(String title, String description, DocumentCategory category,
                                    User owner, Document.DocumentStatus status, Document.DocumentClassification classification) {
        Document document = new Document();
        document.setTitle(title);
        document.setDescription(description);
        document.setCategory(category);
        document.setOwner(owner);
        document.setStatus(status);
        document.setClassification(classification);

        // 设置虚拟文件信息
        document.setFileName("sample_" + System.currentTimeMillis() + ".pdf");
        document.setOriginalFileName(title + ".pdf");
        document.setFilePath("/uploads/samples/" + document.getFileName());
        document.setFileSize(1024L * (100 + (int)(Math.random() * 900))); // 100KB-1MB随机大小
        document.setMimeType("application/pdf");
        document.setFileExtension("pdf");
        document.setChecksum("sample_checksum_" + System.currentTimeMillis());

        // 设置版本信息
        document.setVersionNumber(1);
        document.setVersionLabel("v1.0");
        document.setIsCurrentVersion(true);

        // 设置随机的查看和下载次数
        document.setViewCount((long)(Math.random() * 50));
        document.setDownloadCount((long)(Math.random() * 20));

        documentRepository.save(document);
    }
}
