package com.pharma.dms.repository;

import com.pharma.dms.entity.Department;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DepartmentRepository extends JpaRepository<Department, Long> {

    Optional<Department> findByName(String name);

    Optional<Department> findByCode(String code);

    Boolean existsByName(String name);

    Boolean existsByCode(String code);

    List<Department> findByIsActiveTrue();

    List<Department> findByIsActiveFalse();

    @Query("SELECT d FROM Department d ORDER BY d.name")
    List<Department> findAllOrderByName();
}
