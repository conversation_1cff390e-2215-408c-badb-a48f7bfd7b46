package com.pharma.dms.entity;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

@Entity
@Table(name = "training_courses")
public class TrainingCourse extends BaseEntity {

    @NotBlank
    @Size(max = 255)
    @Column(name = "title", nullable = false)
    private String title;

    @Size(max = 50)
    @Column(name = "course_code", unique = true)
    private String courseCode;

    @Size(max = 1000)
    @Column(name = "description")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "course_type", nullable = false)
    private CourseType courseType = CourseType.GENERAL;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private CourseStatus status = CourseStatus.DRAFT;

    @Column(name = "duration_minutes")
    private Integer durationMinutes;

    @Column(name = "passing_score", nullable = false)
    private Integer passingScore = 80;

    @Column(name = "max_attempts", nullable = false)
    private Integer maxAttempts = 3;

    @Column(name = "is_mandatory", nullable = false)
    private Boolean isMandatory = false;

    @Column(name = "validity_period_months")
    private Integer validityPeriodMonths;

    @Column(name = "auto_assign", nullable = false)
    private Boolean autoAssign = false;

    @Size(max = 500)
    @Column(name = "learning_objectives")
    private String learningObjectives;

    @Size(max = 500)
    @Column(name = "prerequisites")
    private String prerequisites;

    @Column(name = "effective_date")
    private LocalDateTime effectiveDate;

    @Column(name = "expiry_date")
    private LocalDateTime expiryDate;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "instructor_id")
    private User instructor;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approver_id")
    private User approver;

    @Column(name = "approval_date")
    private LocalDateTime approvalDate;

    // Related SOP document for GMP compliance
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "related_document_id")
    private Document relatedDocument;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "course_departments",
               joinColumns = @JoinColumn(name = "course_id"),
               inverseJoinColumns = @JoinColumn(name = "department_id"))
    private Set<Department> targetDepartments = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "course_roles",
               joinColumns = @JoinColumn(name = "course_id"),
               inverseJoinColumns = @JoinColumn(name = "role_id"))
    private Set<Role> targetRoles = new HashSet<>();

    @OneToMany(mappedBy = "course", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<TrainingModule> modules = new ArrayList<>();

    @OneToMany(mappedBy = "course", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<TrainingAssignment> assignments = new ArrayList<>();

    @OneToMany(mappedBy = "course", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<TrainingRecord> trainingRecords = new ArrayList<>();

    // Constructors
    public TrainingCourse() {}

    public TrainingCourse(String title, String courseCode) {
        this.title = title;
        this.courseCode = courseCode;
    }

    // Getters and Setters
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCourseCode() {
        return courseCode;
    }

    public void setCourseCode(String courseCode) {
        this.courseCode = courseCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public CourseType getCourseType() {
        return courseType;
    }

    public void setCourseType(CourseType courseType) {
        this.courseType = courseType;
    }

    public CourseStatus getStatus() {
        return status;
    }

    public void setStatus(CourseStatus status) {
        this.status = status;
    }

    public Integer getDurationMinutes() {
        return durationMinutes;
    }

    public void setDurationMinutes(Integer durationMinutes) {
        this.durationMinutes = durationMinutes;
    }

    public Integer getPassingScore() {
        return passingScore;
    }

    public void setPassingScore(Integer passingScore) {
        this.passingScore = passingScore;
    }

    public Integer getMaxAttempts() {
        return maxAttempts;
    }

    public void setMaxAttempts(Integer maxAttempts) {
        this.maxAttempts = maxAttempts;
    }

    public Boolean getIsMandatory() {
        return isMandatory;
    }

    public void setIsMandatory(Boolean isMandatory) {
        this.isMandatory = isMandatory;
    }

    public Integer getValidityPeriodMonths() {
        return validityPeriodMonths;
    }

    public void setValidityPeriodMonths(Integer validityPeriodMonths) {
        this.validityPeriodMonths = validityPeriodMonths;
    }

    public Boolean getAutoAssign() {
        return autoAssign;
    }

    public void setAutoAssign(Boolean autoAssign) {
        this.autoAssign = autoAssign;
    }

    public String getLearningObjectives() {
        return learningObjectives;
    }

    public void setLearningObjectives(String learningObjectives) {
        this.learningObjectives = learningObjectives;
    }

    public String getPrerequisites() {
        return prerequisites;
    }

    public void setPrerequisites(String prerequisites) {
        this.prerequisites = prerequisites;
    }

    public LocalDateTime getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDateTime effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public LocalDateTime getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(LocalDateTime expiryDate) {
        this.expiryDate = expiryDate;
    }

    public User getInstructor() {
        return instructor;
    }

    public void setInstructor(User instructor) {
        this.instructor = instructor;
    }

    public User getApprover() {
        return approver;
    }

    public void setApprover(User approver) {
        this.approver = approver;
    }

    public LocalDateTime getApprovalDate() {
        return approvalDate;
    }

    public void setApprovalDate(LocalDateTime approvalDate) {
        this.approvalDate = approvalDate;
    }

    public Document getRelatedDocument() {
        return relatedDocument;
    }

    public void setRelatedDocument(Document relatedDocument) {
        this.relatedDocument = relatedDocument;
    }

    public Set<Department> getTargetDepartments() {
        return targetDepartments;
    }

    public void setTargetDepartments(Set<Department> targetDepartments) {
        this.targetDepartments = targetDepartments;
    }

    public Set<Role> getTargetRoles() {
        return targetRoles;
    }

    public void setTargetRoles(Set<Role> targetRoles) {
        this.targetRoles = targetRoles;
    }

    public List<TrainingModule> getModules() {
        return modules;
    }

    public void setModules(List<TrainingModule> modules) {
        this.modules = modules;
    }

    public List<TrainingAssignment> getAssignments() {
        return assignments;
    }

    public void setAssignments(List<TrainingAssignment> assignments) {
        this.assignments = assignments;
    }

    public List<TrainingRecord> getTrainingRecords() {
        return trainingRecords;
    }

    public void setTrainingRecords(List<TrainingRecord> trainingRecords) {
        this.trainingRecords = trainingRecords;
    }

    // Helper methods
    public boolean isActive() {
        return status == CourseStatus.ACTIVE;
    }

    public boolean isExpired() {
        return expiryDate != null && LocalDateTime.now().isAfter(expiryDate);
    }

    public boolean isEffective() {
        return effectiveDate == null || LocalDateTime.now().isAfter(effectiveDate);
    }

    public String getFormattedDuration() {
        if (durationMinutes == null) return "Not specified";
        
        int hours = durationMinutes / 60;
        int minutes = durationMinutes % 60;
        
        if (hours > 0) {
            return hours + "h " + (minutes > 0 ? minutes + "m" : "");
        } else {
            return minutes + "m";
        }
    }

    // Enums
    public enum CourseType {
        GENERAL("General Training"),
        GMP("GMP Training"),
        SOP("SOP Training"),
        SAFETY("Safety Training"),
        QUALITY("Quality Training"),
        REGULATORY("Regulatory Training"),
        TECHNICAL("Technical Training"),
        COMPLIANCE("Compliance Training");

        private final String displayName;

        CourseType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    public enum CourseStatus {
        DRAFT("Draft"),
        UNDER_REVIEW("Under Review"),
        APPROVED("Approved"),
        ACTIVE("Active"),
        INACTIVE("Inactive"),
        ARCHIVED("Archived");

        private final String displayName;

        CourseStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}
