<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 制药文档管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" style="min-height: 100vh; background-color: #f8f9fa; border-right: 1px solid #dee2e6;">
                <div class="position-sticky pt-3">
                    <div class="navbar-brand mb-4" style="font-weight: bold; color: #0d6efd !important;">
                        <i class="fas fa-pills me-2"></i>
                        制药文档管理系统
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/users" style="background-color: #0d6efd; color: white !important; border-radius: 0.375rem;">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/documents" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-file-alt me-2"></i>
                                文档管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/system-overview" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-line me-2"></i>
                                系统概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/my-training" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user-graduate me-2"></i>
                                我的培训
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/training-courses" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-graduation-cap me-2"></i>
                                培训课程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-bar me-2"></i>
                                报表管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-cog me-2"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>

                    <hr>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/profile" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user me-2"></i>
                                个人资料
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">用户管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                <i class="fas fa-user-plus me-1"></i>添加新用户
                            </button>
                            <button class="btn btn-success" onclick="importUsers()" title="导入用户">
                                <i class="fas fa-file-import me-1"></i>导入用户
                            </button>
                        </div>
                        <div class="btn-group me-2">
                            <button class="btn btn-outline-info" onclick="exportUsers()" title="导出用户">
                                <i class="fas fa-file-export me-1"></i>导出
                            </button>
                            <button class="btn btn-outline-warning" onclick="showBulkOperations()" title="批量操作">
                                <i class="fas fa-tasks me-1"></i>批量操作
                            </button>
                            <button class="btn btn-outline-secondary" onclick="showUserTemplates()" title="用户模板">
                                <i class="fas fa-user-cog me-1"></i>模板
                            </button>
                        </div>
                        <div class="btn-group me-2">
                            <button class="btn btn-outline-primary" onclick="showUserStats()" title="用户统计">
                                <i class="fas fa-chart-bar me-1"></i>统计
                            </button>
                            <button class="btn btn-outline-success" onclick="showActiveUsers()" title="活跃用户">
                                <i class="fas fa-user-check me-1"></i>活跃用户
                            </button>
                            <button class="btn btn-outline-danger" onclick="showInactiveUsers()" title="非活跃用户">
                                <i class="fas fa-user-times me-1"></i>非活跃用户
                            </button>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary" onclick="refreshUsers()" title="刷新列表">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" title="更多操作">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="resetAllPasswords()"><i class="fas fa-key me-2"></i>重置所有密码</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendWelcomeEmails()"><i class="fas fa-envelope me-2"></i>发送欢迎邮件</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="archiveInactiveUsers()"><i class="fas fa-archive me-2"></i>归档非活跃用户</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="showUserSettings()"><i class="fas fa-cog me-2"></i>用户设置</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Page content -->

        <!-- Users Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">系统用户</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="usersTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>姓名</th>
                                <th>邮箱</th>
                                <th>部门</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>最后登录</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- Users will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Add User Modal -->
        <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="addUserForm">
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Username *</label>
                                        <input type="text" class="form-control" id="username" name="username" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email *</label>
                                        <input type="email" class="form-control" id="email" name="email" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="firstName" class="form-label">First Name *</label>
                                        <input type="text" class="form-control" id="firstName" name="firstName" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="lastName" class="form-label">Last Name *</label>
                                        <input type="text" class="form-control" id="lastName" name="lastName" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="password" class="form-label">Password *</label>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">Phone</label>
                                        <input type="tel" class="form-control" id="phone" name="phone">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="departmentId" class="form-label">Department</label>
                                        <select class="form-select" id="departmentId" name="departmentId">
                                            <option value="">Select Department</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="role" class="form-label">Role *</label>
                                        <select class="form-select" id="role" name="role" required>
                                            <option value="user">User</option>
                                            <option value="qa">QA</option>
                                            <option value="admin">Admin</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Add User</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Edit User Modal -->
        <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="editUserForm">
                        <div class="modal-body">
                            <input type="hidden" id="editUserId" name="userId">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editUsername" class="form-label">Username</label>
                                        <input type="text" class="form-control" id="editUsername" name="username" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editEmail" class="form-label">Email *</label>
                                        <input type="email" class="form-control" id="editEmail" name="email" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editFirstName" class="form-label">First Name *</label>
                                        <input type="text" class="form-control" id="editFirstName" name="firstName" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editLastName" class="form-label">Last Name *</label>
                                        <input type="text" class="form-control" id="editLastName" name="lastName" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editPhone" class="form-label">Phone</label>
                                        <input type="tel" class="form-control" id="editPhone" name="phone">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editDepartment" class="form-label">Department</label>
                                        <select class="form-select" id="editDepartment" name="departmentId">
                                            <option value="">Select Department</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="editIsActive" name="isActive">
                                            <label class="form-check-label" for="editIsActive">
                                                Active User
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Update User</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Change Password Modal -->
        <div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="changePasswordModalLabel">Change Password</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="changePasswordForm">
                        <div class="modal-body">
                            <input type="hidden" id="passwordUserId" name="userId">
                            <div class="mb-3">
                                <label for="newPassword" class="form-label">New Password *</label>
                                <input type="password" class="form-control" id="newPassword" name="newPassword" required minlength="6">
                            </div>
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">Confirm Password *</label>
                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required minlength="6">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Change Password</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
            loadDepartments();
        });

        async function loadUsers() {
            try {
                console.log('=== 加载用户列表开始 ===');

                const response = await authUtils.secureApiCall('/dms/api/users');
                console.log('用户列表API响应状态:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('用户列表API响应数据:', result);

                    if (result.success && result.data) {
                        // 处理不同的数据结构
                        let userList = [];
                        if (Array.isArray(result.data)) {
                            userList = result.data;
                        } else if (result.data.content && Array.isArray(result.data.content)) {
                            userList = result.data.content;
                        } else {
                            console.log('未知的数据结构:', result.data);
                            userList = [];
                        }

                        console.log('处理后的用户列表:', userList);
                        console.log('用户列表类型:', typeof userList, Array.isArray(userList));

                        if (Array.isArray(userList)) {
                            displayUsers(userList);
                            console.log('✅ 用户列表加载成功，用户数量:', userList.length);
                        } else {
                            console.error('❌ 用户数据不是数组格式');
                            loadMockUsers();
                        }
                    } else {
                        console.log('❌ 用户列表API返回失败:', result.message);
                        loadMockUsers();
                    }
                } else {
                    console.log('❌ 用户列表API请求失败:', response.status);
                    const errorText = await response.text();
                    console.log('错误响应内容:', errorText);

                    document.getElementById('usersTableBody').innerHTML =
                        '<tr><td colspan="9" class="text-center text-muted">加载用户失败: HTTP ' + response.status + '</td></tr>';
                }
            } catch (error) {
                console.error('加载用户列表异常:', error);
                document.getElementById('usersTableBody').innerHTML =
                    '<tr><td colspan="9" class="text-center text-muted">网络错误，请稍后重试</td></tr>';
            }
        }

        function loadMockUsers() {
            console.log('使用模拟用户数据');
            const mockUsers = [
                {
                    id: 1,
                    username: 'admin',
                    firstName: 'System',
                    lastName: 'Administrator',
                    email: '<EMAIL>',
                    department: { name: 'IT部' },
                    roles: [{ name: 'ROLE_ADMIN' }],
                    isActive: true,
                    isLocked: false,
                    lastLogin: '2024-01-01T00:00:00'
                },
                {
                    id: 2,
                    username: 'qa_user',
                    firstName: 'Quality',
                    lastName: 'Assurance',
                    email: '<EMAIL>',
                    department: { name: '质量保证部' },
                    roles: [{ name: 'ROLE_QA' }],
                    isActive: true,
                    isLocked: false,
                    lastLogin: '2024-01-02T00:00:00'
                },
                {
                    id: 3,
                    username: 'user',
                    firstName: 'Regular',
                    lastName: 'User',
                    email: '<EMAIL>',
                    department: { name: '生产部' },
                    roles: [{ name: 'ROLE_USER' }],
                    isActive: true,
                    isLocked: false,
                    lastLogin: '2024-01-03T00:00:00'
                }
            ];

            displayUsers(mockUsers);

            // 显示警告信息
            const tbody = document.getElementById('usersTableBody');
            const warningRow = document.createElement('tr');
            warningRow.innerHTML = '<td colspan="9" class="text-center text-warning bg-warning-subtle">⚠️ 使用模拟数据，请检查后端API连接</td>';
            tbody.insertBefore(warningRow, tbody.firstChild);
        }

        function displayUsers(users) {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            if (!Array.isArray(users)) {
                console.error('displayUsers: 参数不是数组', users);
                tbody.innerHTML = '<tr><td colspan="9" class="text-center text-danger">数据格式错误</td></tr>';
                return;
            }

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.username}</td>
                    <td>${user.firstName} ${user.lastName}</td>
                    <td>${user.email}</td>
                    <td>${user.department ? user.department.name : '未分配'}</td>
                    <td>
                        ${user.roles ? user.roles.map(role =>
                            `<span class="badge bg-primary me-1">${role.name ? role.name.replace('ROLE_', '') : role}</span>`
                        ).join('') : '<span class="badge bg-secondary">USER</span>'}
                    </td>
                    <td>
                        <span class="badge ${user.isActive ? 'bg-success' : 'bg-danger'}">
                            ${user.isActive ? '活跃' : '禁用'}
                        </span>
                    </td>
                    <td>${user.lastLogin ? new Date(user.lastLogin).toLocaleString() : '从未登录'}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editUser(${user.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info me-1" onclick="changePassword(${user.id})" title="修改密码">
                            <i class="fas fa-key"></i>
                        </button>
                        ${!user.isLocked ?
                            `<button class="btn btn-sm btn-outline-warning me-1" onclick="lockUser(${user.id})" title="锁定用户">
                                <i class="fas fa-lock"></i>
                            </button>` :
                            `<button class="btn btn-sm btn-outline-success me-1" onclick="unlockUser(${user.id})" title="解锁用户">
                                <i class="fas fa-unlock"></i>
                            </button>`
                        }
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        async function loadDepartments() {
            // Demo departments
            const departments = [
                { id: 1, name: 'Quality Assurance' },
                { id: 2, name: 'Research & Development' },
                { id: 3, name: 'Manufacturing' },
                { id: 4, name: 'Regulatory Affairs' },
                { id: 5, name: 'Information Technology' },
                { id: 6, name: 'Human Resources' },
                { id: 7, name: 'Finance' }
            ];

            const select = document.getElementById('departmentId');
            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.id;
                option.textContent = dept.name;
                select.appendChild(option);
            });
        }

        document.getElementById('addUserForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const userData = {
                username: formData.get('username'),
                email: formData.get('email'),
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                password: formData.get('password'),
                phone: formData.get('phone'),
                departmentId: formData.get('departmentId') || null,
                role: [formData.get('role')]
            };

            try {
                const response = await authUtils.secureApiCall('/dms/api/users', {
                    method: 'POST',
                    body: JSON.stringify(userData)
                });

                const result = await response.json();

                if (response.ok) {
                    alert('用户创建成功！');
                    bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
                    this.reset();
                    loadUsers();
                } else {
                    alert('创建用户失败：' + result.message);
                }
            } catch (error) {
                alert('创建用户失败：网络错误');
                console.error('Error:', error);
            }
        });

        async function editUser(userId) {
            try {
                const response = await authUtils.secureApiCall(`/dms/api/users/${userId}`);

                if (response.ok) {
                    const result = await response.json();
                    const user = result.data;

                    // Populate edit form
                    document.getElementById('editUserId').value = user.id;
                    document.getElementById('editUsername').value = user.username;
                    document.getElementById('editEmail').value = user.email;
                    document.getElementById('editFirstName').value = user.firstName;
                    document.getElementById('editLastName').value = user.lastName;
                    document.getElementById('editPhone').value = user.phone || '';
                    document.getElementById('editDepartment').value = user.department ? user.department.id : '';
                    document.getElementById('editIsActive').checked = user.isActive;

                    // Load departments for edit form
                    await loadDepartmentsForEdit();

                    // Show modal
                    new bootstrap.Modal(document.getElementById('editUserModal')).show();
                } else {
                    alert('Failed to load user details');
                }
            } catch (error) {
                alert('Error loading user: ' + error.message);
            }
        }

        async function deleteUser(userId) {
            if (confirm('确定要删除这个用户吗？\n注意：这将停用用户账户，而不是永久删除。')) {
                try {
                    console.log('=== 删除用户开始 ===');
                    console.log('用户ID:', userId);

                    const response = await authUtils.secureApiCall(`/dms/api/users/${userId}`, {
                        method: 'DELETE'
                    });

                    console.log('删除用户API响应状态:', response.status);

                    if (response.ok) {
                        const result = await response.json();
                        console.log('删除用户API响应数据:', result);
                        alert('用户删除成功！用户已被停用。');
                        loadUsers();
                        console.log('✅ 用户删除成功');
                    } else {
                        const result = await response.json();
                        console.error('❌ 删除用户失败:', result);
                        alert('删除用户失败: ' + (result.message || '未知错误'));
                    }
                } catch (error) {
                    console.error('❌ 删除用户异常:', error);
                    alert('删除用户失败: ' + error.message);
                }
            }
        }

        function changePassword(userId) {
            document.getElementById('passwordUserId').value = userId;
            new bootstrap.Modal(document.getElementById('changePasswordModal')).show();
        }

        async function lockUser(userId) {
            try {
                const response = await authUtils.secureApiCall(`/dms/api/users/${userId}/lock`, {
                    method: 'POST'
                });

                const result = await response.json();
                if (response.ok) {
                    alert('User locked successfully');
                    loadUsers();
                } else {
                    alert('Error locking user: ' + result.message);
                }
            } catch (error) {
                alert('Error locking user: ' + error.message);
            }
        }

        async function unlockUser(userId) {
            try {
                const response = await authUtils.secureApiCall(`/dms/api/users/${userId}/unlock`, {
                    method: 'POST'
                });

                const result = await response.json();
                if (response.ok) {
                    alert('User unlocked successfully');
                    loadUsers();
                } else {
                    alert('Error unlocking user: ' + result.message);
                }
            } catch (error) {
                alert('Error unlocking user: ' + error.message);
            }
        }

        async function loadDepartmentsForEdit() {
            const departments = [
                { id: 1, name: 'Quality Assurance' },
                { id: 2, name: 'Research & Development' },
                { id: 3, name: 'Manufacturing' },
                { id: 4, name: 'Regulatory Affairs' },
                { id: 5, name: 'Information Technology' },
                { id: 6, name: 'Human Resources' },
                { id: 7, name: 'Finance' }
            ];

            const select = document.getElementById('editDepartment');
            // Clear existing options except the first one
            select.innerHTML = '<option value="">Select Department</option>';

            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.id;
                option.textContent = dept.name;
                select.appendChild(option);
            });
        }

        // Edit user form submission
        document.getElementById('editUserForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const userId = formData.get('userId');
            const userData = {
                email: formData.get('email'),
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                phone: formData.get('phone'),
                departmentId: formData.get('departmentId') || null,
                isActive: formData.has('isActive')
            };

            try {
                const response = await authUtils.secureApiCall(`/dms/api/users/${userId}`, {
                    method: 'PUT',
                    body: JSON.stringify(userData)
                });

                const result = await response.json();

                if (response.ok) {
                    alert('User updated successfully!');
                    bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
                    loadUsers();
                } else {
                    alert('Error updating user: ' + result.message);
                }
            } catch (error) {
                alert('Error updating user: Network error');
                console.error('Error:', error);
            }
        });

        // Change password form submission
        document.getElementById('changePasswordForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const userId = formData.get('userId');
            const newPassword = formData.get('newPassword');
            const confirmPassword = formData.get('confirmPassword');

            if (newPassword !== confirmPassword) {
                alert('Passwords do not match!');
                return;
            }

            try {
                const response = await authUtils.secureApiCall(`/dms/api/users/${userId}/reset-password`, {
                    method: 'POST',
                    body: JSON.stringify({ newPassword: newPassword })
                });

                const result = await response.json();

                if (response.ok) {
                    alert('Password changed successfully!');
                    bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
                    this.reset();
                } else {
                    alert('Error changing password: ' + result.message);
                }
            } catch (error) {
                alert('Error changing password: Network error');
                console.error('Error:', error);
            }
        });

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }

        // 新增功能实现
        function importUsers() {
            const importModal = document.createElement('div');
            importModal.className = 'modal fade';
            importModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">导入用户</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">选择Excel文件</label>
                                <input type="file" class="form-control" accept=".xlsx,.xls,.csv">
                                <div class="form-text">支持Excel和CSV格式文件</div>
                            </div>
                            <div class="mb-3">
                                <a href="/dms/templates/user-import-template.xlsx" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-download me-1"></i>下载模板
                                </a>
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                请确保Excel文件包含：用户名、邮箱、姓名、部门等必要字段
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="executeImport()">开始导入</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(importModal);
            const modal = new bootstrap.Modal(importModal);
            modal.show();

            importModal.addEventListener('hidden.bs.modal', () => {
                importModal.remove();
            });
        }

        function executeImport() {
            alert('用户导入功能开发中');
        }

        function exportUsers() {
            const exportModal = document.createElement('div');
            exportModal.className = 'modal fade';
            exportModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">导出用户</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">导出格式</label>
                                <select class="form-select">
                                    <option value="excel">Excel表格</option>
                                    <option value="csv">CSV文件</option>
                                    <option value="pdf">PDF报告</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">导出内容</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" checked>
                                    <label class="form-check-label">基本信息</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox">
                                    <label class="form-check-label">角色权限</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox">
                                    <label class="form-check-label">登录记录</label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="executeExport()">开始导出</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(exportModal);
            const modal = new bootstrap.Modal(exportModal);
            modal.show();

            exportModal.addEventListener('hidden.bs.modal', () => {
                exportModal.remove();
            });
        }

        function executeExport() {
            alert('用户导出功能开发中');
        }

        function showBulkOperations() {
            alert('批量操作功能开发中');
        }

        function showUserTemplates() {
            alert('用户模板功能开发中');
        }

        function showUserStats() {
            alert('用户统计功能开发中');
        }

        function showActiveUsers() {
            // 筛选活跃用户
            alert('显示活跃用户');
        }

        function showInactiveUsers() {
            // 筛选非活跃用户
            alert('显示非活跃用户');
        }

        function refreshUsers() {
            loadUsers();
            alert('用户列表已刷新');
        }

        function resetAllPasswords() {
            if (confirm('确定要重置所有用户密码吗？此操作不可撤销！')) {
                alert('重置密码功能开发中');
            }
        }

        function sendWelcomeEmails() {
            if (confirm('确定要发送欢迎邮件给所有新用户吗？')) {
                alert('发送邮件功能开发中');
            }
        }

        function archiveInactiveUsers() {
            if (confirm('确定要归档所有非活跃用户吗？')) {
                alert('归档用户功能开发中');
            }
        }

        function showUserSettings() {
            alert('用户设置功能开发中');
        }
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 认证工具 -->
    <script src="/dms/js/auth.js"></script>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
