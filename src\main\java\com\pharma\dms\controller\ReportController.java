package com.pharma.dms.controller;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.User;
import com.pharma.dms.security.UserPrincipal;
import com.pharma.dms.service.UserService;

@Controller
@RequestMapping("/dms")
public class ReportController {

    private static final Logger logger = LoggerFactory.getLogger(ReportController.class);

    @Autowired
    private UserService userService;

    /**
     * 报表管理页面
     */
    @GetMapping("/reports")
    @PreAuthorize("hasRole('USER')")
    public String reportsPage(Model model, @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            User user = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            model.addAttribute("user", user);
            model.addAttribute("currentTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            return "reports";
        } catch (Exception e) {
            logger.error("Error loading reports page", e);
            model.addAttribute("error", "加载报表页面失败");
            return "error";
        }
    }

    /**
     * 系统设置页面
     */
    @GetMapping("/settings")
    @PreAuthorize("hasRole('ADMIN')")
    public String settingsPage(Model model, @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            User user = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            model.addAttribute("user", user);
            model.addAttribute("currentTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            return "settings";
        } catch (Exception e) {
            logger.error("Error loading settings page", e);
            model.addAttribute("error", "加载系统设置页面失败");
            return "error";
        }
    }

    /**
     * API: 获取用户活动报表
     */
    @GetMapping("/api/reports/user-activity")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserActivityReport(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "30") int days) {
        
        try {
            Map<String, Object> report = new HashMap<>();
            
            // 模拟数据 - 实际应该从数据库查询
            report.put("totalUsers", 25);
            report.put("activeUsers", 18);
            report.put("newUsers", 3);
            report.put("loginCount", 156);
            report.put("avgSessionTime", "45分钟");
            
            // 每日活动数据
            List<Map<String, Object>> dailyActivity = List.of(
                Map.of("date", "2025-06-01", "users", 15, "logins", 23),
                Map.of("date", "2025-06-02", "users", 18, "logins", 28),
                Map.of("date", "2025-06-03", "users", 16, "logins", 25),
                Map.of("date", "2025-06-04", "users", 20, "logins", 32),
                Map.of("date", "2025-06-05", "users", 18, "logins", 29)
            );
            report.put("dailyActivity", dailyActivity);
            
            return ResponseEntity.ok(ApiResponse.success("用户活动报表", report));
        } catch (Exception e) {
            logger.error("Error generating user activity report", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("生成用户活动报表失败", e.getMessage()));
        }
    }

    /**
     * API: 获取文档统计报表
     */
    @GetMapping("/api/reports/document-stats")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getDocumentStatsReport() {
        try {
            Map<String, Object> report = new HashMap<>();
            
            // 模拟数据
            report.put("totalDocuments", 156);
            report.put("activeDocuments", 142);
            report.put("pendingApproval", 8);
            report.put("archivedDocuments", 6);
            
            // 按类型分布
            List<Map<String, Object>> byCategory = List.of(
                Map.of("category", "SOP", "count", 45, "percentage", 28.8),
                Map.of("category", "质量手册", "count", 32, "percentage", 20.5),
                Map.of("category", "验证文档", "count", 28, "percentage", 17.9),
                Map.of("category", "培训材料", "count", 25, "percentage", 16.0),
                Map.of("category", "其他", "count", 26, "percentage", 16.8)
            );
            report.put("byCategory", byCategory);
            
            return ResponseEntity.ok(ApiResponse.success("文档统计报表", report));
        } catch (Exception e) {
            logger.error("Error generating document stats report", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("生成文档统计报表失败", e.getMessage()));
        }
    }

    /**
     * API: 获取培训统计报表
     */
    @GetMapping("/api/reports/training-stats")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getTrainingStatsReport() {
        try {
            Map<String, Object> report = new HashMap<>();
            
            // 模拟数据
            report.put("totalCourses", 24);
            report.put("activeCourses", 18);
            report.put("completedTrainings", 89);
            report.put("pendingTrainings", 34);
            report.put("averageScore", 87.5);
            report.put("passRate", 94.2);
            
            // 培训完成趋势
            List<Map<String, Object>> completionTrend = List.of(
                Map.of("month", "2025-01", "completed", 12, "passed", 11),
                Map.of("month", "2025-02", "completed", 18, "passed", 17),
                Map.of("month", "2025-03", "completed", 15, "passed", 14),
                Map.of("month", "2025-04", "completed", 22, "passed", 21),
                Map.of("month", "2025-05", "completed", 19, "passed", 18)
            );
            report.put("completionTrend", completionTrend);
            
            return ResponseEntity.ok(ApiResponse.success("培训统计报表", report));
        } catch (Exception e) {
            logger.error("Error generating training stats report", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("生成培训统计报表失败", e.getMessage()));
        }
    }

    /**
     * API: 获取系统配置
     */
    @GetMapping("/api/settings/config")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            
            // 系统基本配置
            config.put("systemName", "制药文档管理系统");
            config.put("version", "1.0.0");
            config.put("maxFileSize", "100MB");
            config.put("sessionTimeout", "30分钟");
            config.put("passwordPolicy", "最少8位，包含大小写字母和数字");
            
            // 功能开关
            Map<String, Boolean> features = new HashMap<>();
            features.put("aiAnalysis", true);
            features.put("ocrEnabled", false);
            features.put("emailNotifications", true);
            features.put("auditLogging", true);
            features.put("documentVersioning", true);
            config.put("features", features);
            
            return ResponseEntity.ok(ApiResponse.success("系统配置", config));
        } catch (Exception e) {
            logger.error("Error getting system config", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取系统配置失败", e.getMessage()));
        }
    }

    /**
     * API: 更新系统配置
     */
    @PostMapping("/api/settings/config")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<String>> updateSystemConfig(
            @RequestBody Map<String, Object> config,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            // 这里应该保存配置到数据库
            logger.info("System config updated by user: {}", userPrincipal.getUsername());
            logger.info("New config: {}", config);
            
            return ResponseEntity.ok(ApiResponse.success("系统配置更新成功"));
        } catch (Exception e) {
            logger.error("Error updating system config", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("更新系统配置失败", e.getMessage()));
        }
    }
}
