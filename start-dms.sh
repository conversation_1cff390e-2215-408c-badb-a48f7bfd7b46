#!/bin/bash

# 制药DMS系统一键启动脚本
# 适用于 Git Bash 环境

echo "=========================================="
echo "🏥 制药文档管理系统 (Pharmaceutical DMS)"
echo "🚀 一键启动脚本"
echo "=========================================="

# 脚本路径
SCRIPT_DIR="/d/learn/java/dms"
cd "$SCRIPT_DIR"

# 显示系统信息
echo "📊 系统信息:"
echo "   操作系统: $(uname -s)"
echo "   当前时间: $(date)"
echo "   工作目录: $(pwd)"
echo ""

# 第一步：启动数据库
echo "🗄️  第一步：启动PostgreSQL数据库"
echo "----------------------------------------"
if [ -f "start-database.sh" ]; then
    bash start-database.sh
    if [ $? -ne 0 ]; then
        echo "❌ 数据库启动失败，退出"
        exit 1
    fi
else
    echo "❌ 数据库启动脚本未找到: start-database.sh"
    exit 1
fi

echo ""
echo "⏳ 等待数据库完全启动..."
sleep 5

# 第二步：启动应用程序
echo "🚀 第二步：启动DMS应用程序"
echo "----------------------------------------"
if [ -f "start-application.sh" ]; then
    bash start-application.sh
    if [ $? -ne 0 ]; then
        echo "❌ 应用程序启动失败，退出"
        exit 1
    fi
else
    echo "❌ 应用程序启动脚本未找到: start-application.sh"
    exit 1
fi

echo ""

# 第三步：验证系统状态
echo "🔍 第三步：验证系统状态"
echo "----------------------------------------"

# 检查数据库状态
echo "📊 数据库状态:"
if pgrep -f postgres > /dev/null; then
    echo "   ✅ PostgreSQL: 运行中"
else
    echo "   ❌ PostgreSQL: 未运行"
fi

# 检查应用程序状态
echo "📊 应用程序状态:"
if pgrep -f "dms-1.0.0.jar" > /dev/null; then
    echo "   ✅ DMS应用: 运行中"
    APP_PID=$(pgrep -f "dms-1.0.0.jar")
    echo "   📋 进程ID: $APP_PID"
else
    echo "   ❌ DMS应用: 未运行"
fi

# 检查端口状态
echo "📊 端口状态:"
if netstat -an 2>/dev/null | grep ":5432" | grep LISTEN > /dev/null; then
    echo "   ✅ PostgreSQL端口 5432: 监听中"
else
    echo "   ⚠️  PostgreSQL端口 5432: 未监听"
fi

if netstat -an 2>/dev/null | grep ":8081" | grep LISTEN > /dev/null; then
    echo "   ✅ DMS应用端口 8081: 监听中"
else
    echo "   ⚠️  DMS应用端口 8081: 未监听"
fi

echo ""

# 第四步：显示访问信息
echo "🌐 第四步：系统访问信息"
echo "----------------------------------------"
echo "📱 Web访问地址:"
echo "   🏠 本地访问: http://localhost:8081/dms/login"
echo "   🌍 网络访问: http://**************:8081/dms/login"
echo ""
echo "🔧 管理地址:"
echo "   📊 健康检查: http://localhost:8081/dms/api/health"
echo "   📈 网络信息: http://localhost:8081/dms/api/health/network"
echo "   🗄️  H2控制台: http://localhost:8081/dms/h2-console (如使用H2)"
echo ""
echo "👤 默认登录信息:"
echo "   用户名: admin"
echo "   密码: admin123"
echo ""

# 第五步：提供管理选项
echo "🛠️  第五步：管理选项"
echo "----------------------------------------"
echo "📋 可用命令:"
echo "   查看应用日志: tail -f application.log"
echo "   停止应用程序: pkill -f dms-1.0.0.jar"
echo "   停止数据库: /d/sql/pgsql/bin/pg_ctl -D /d/sql/pgsql/data stop"
echo "   重启系统: bash start-dms.sh"
echo ""

# 创建停止脚本
cat > stop-dms.sh << 'EOF'
#!/bin/bash
echo "🛑 停止制药DMS系统..."
echo "停止应用程序..."
pkill -f "dms-1.0.0.jar"
echo "停止数据库..."
/d/sql/pgsql/bin/pg_ctl -D /d/sql/pgsql/data stop
echo "✅ 系统已停止"
EOF

chmod +x stop-dms.sh

echo "💾 已创建停止脚本: stop-dms.sh"
echo ""

echo "=========================================="
echo "🎉 制药DMS系统启动完成!"
echo "🌐 请访问: http://**************:8081/dms/login"
echo "=========================================="

# 可选：自动打开浏览器（如果在Windows环境下）
read -p "是否自动打开浏览器? (y/n): " open_browser
if [ "$open_browser" = "y" ] || [ "$open_browser" = "Y" ]; then
    if command -v cmd.exe &> /dev/null; then
        cmd.exe /c start http://**************:8081/dms/login
    fi
fi
