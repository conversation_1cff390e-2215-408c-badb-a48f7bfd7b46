package com.pharma.dms.controller;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.Document;
import com.pharma.dms.entity.User;
import com.pharma.dms.security.UserPrincipal;
import com.pharma.dms.service.DocumentService;
import com.pharma.dms.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/documents/approval")
public class DocumentApprovalController {

    @Autowired
    private DocumentService documentService;

    @Autowired
    private UserService userService;

    /**
     * 获取待审批文档列表
     */
    @GetMapping("/pending")
    @PreAuthorize("hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<Map<String, Object>>>> getPendingApprovals(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                       Sort.by(sortBy).descending() : 
                       Sort.by(sortBy).ascending();
            
            Pageable pageable = PageRequest.of(page, size, sort);
            
            // 获取状态为UNDER_REVIEW的文档
            Page<Document> pendingDocs = documentService.getDocumentsByStatus(
                Document.DocumentStatus.UNDER_REVIEW, pageable);

            // 转换为简化的响应数据
            Page<Map<String, Object>> responseData = pendingDocs.map(doc -> {
                Map<String, Object> docData = new HashMap<>();
                docData.put("id", doc.getId());
                docData.put("title", doc.getTitle());
                docData.put("description", doc.getDescription());
                docData.put("fileName", doc.getFileName());
                docData.put("fileSize", doc.getFormattedFileSize());
                docData.put("status", doc.getStatus().toString());
                docData.put("classification", doc.getClassification().toString());
                docData.put("versionNumber", doc.getVersionNumber());
                docData.put("createdAt", doc.getCreatedAt().toString());
                docData.put("owner", Map.of(
                    "id", doc.getOwner().getId(),
                    "username", doc.getOwner().getUsername(),
                    "fullName", doc.getOwner().getFullName()
                ));
                if (doc.getCategory() != null) {
                    docData.put("category", Map.of(
                        "id", doc.getCategory().getId(),
                        "name", doc.getCategory().getName()
                    ));
                }
                return docData;
            });

            return ResponseEntity.ok(ApiResponse.success("Pending approvals retrieved", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to retrieve pending approvals", e.getMessage()));
        }
    }

    /**
     * 提交文档审批
     */
    @PostMapping("/{documentId}/submit")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> submitForApproval(
            @PathVariable Long documentId,
            @RequestBody Map<String, String> request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            Document document = documentService.getDocumentById(documentId)
                    .orElseThrow(() -> new RuntimeException("Document not found"));

            // 检查文档所有者
            if (!document.getOwner().getUsername().equals(userPrincipal.getUsername())) {
                return ResponseEntity.status(403)
                        .body(ApiResponse.error("Only document owner can submit for approval", null));
            }

            // 检查文档状态
            if (document.getStatus() != Document.DocumentStatus.DRAFT) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Only draft documents can be submitted for approval", null));
            }

            String submissionNotes = request.get("submissionNotes");

            // 更新文档状态
            document.setStatus(Document.DocumentStatus.UNDER_REVIEW);
            document.setReviewDate(LocalDateTime.now());
            
            Document updatedDocument = documentService.updateDocument(document);

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", updatedDocument.getId());
            responseData.put("title", updatedDocument.getTitle());
            responseData.put("status", updatedDocument.getStatus().toString());
            responseData.put("reviewDate", updatedDocument.getReviewDate().toString());

            return ResponseEntity.ok(ApiResponse.success("Document submitted for approval", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to submit document for approval", e.getMessage()));
        }
    }

    /**
     * 批准文档
     */
    @PostMapping("/{documentId}/approve")
    @PreAuthorize("hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> approveDocument(
            @PathVariable Long documentId,
            @RequestBody Map<String, String> request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            Document document = documentService.getDocumentById(documentId)
                    .orElseThrow(() -> new RuntimeException("Document not found"));

            // 检查文档状态
            if (document.getStatus() != Document.DocumentStatus.UNDER_REVIEW) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Only documents under review can be approved", null));
            }

            User approver = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            String approvalNotes = request.get("approvalNotes");

            // 更新文档状态
            document.setStatus(Document.DocumentStatus.APPROVED);
            document.setApprovalDate(LocalDateTime.now());
            document.setApprovedBy(approver);
            
            Document updatedDocument = documentService.updateDocument(document);

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", updatedDocument.getId());
            responseData.put("title", updatedDocument.getTitle());
            responseData.put("status", updatedDocument.getStatus().toString());
            responseData.put("approvalDate", updatedDocument.getApprovalDate().toString());
            responseData.put("approvedBy", Map.of(
                "id", approver.getId(),
                "username", approver.getUsername(),
                "fullName", approver.getFullName()
            ));

            return ResponseEntity.ok(ApiResponse.success("Document approved successfully", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to approve document", e.getMessage()));
        }
    }

    /**
     * 拒绝文档
     */
    @PostMapping("/{documentId}/reject")
    @PreAuthorize("hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> rejectDocument(
            @PathVariable Long documentId,
            @RequestBody Map<String, String> request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            Document document = documentService.getDocumentById(documentId)
                    .orElseThrow(() -> new RuntimeException("Document not found"));

            // 检查文档状态
            if (document.getStatus() != Document.DocumentStatus.UNDER_REVIEW) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Only documents under review can be rejected", null));
            }

            String rejectionReason = request.get("rejectionReason");
            if (rejectionReason == null || rejectionReason.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Rejection reason is required", null));
            }

            // 更新文档状态回到草稿
            document.setStatus(Document.DocumentStatus.DRAFT);
            document.setReviewDate(null);
            
            Document updatedDocument = documentService.updateDocument(document);

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", updatedDocument.getId());
            responseData.put("title", updatedDocument.getTitle());
            responseData.put("status", updatedDocument.getStatus().toString());
            responseData.put("rejectionReason", rejectionReason);

            return ResponseEntity.ok(ApiResponse.success("Document rejected", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to reject document", e.getMessage()));
        }
    }

    /**
     * 发布文档
     */
    @PostMapping("/{documentId}/publish")
    @PreAuthorize("hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> publishDocument(
            @PathVariable Long documentId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            Document document = documentService.getDocumentById(documentId)
                    .orElseThrow(() -> new RuntimeException("Document not found"));

            // 检查文档状态
            if (document.getStatus() != Document.DocumentStatus.APPROVED) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Only approved documents can be published", null));
            }

            // 更新文档状态
            document.setStatus(Document.DocumentStatus.PUBLISHED);
            
            Document updatedDocument = documentService.updateDocument(document);

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", updatedDocument.getId());
            responseData.put("title", updatedDocument.getTitle());
            responseData.put("status", updatedDocument.getStatus().toString());

            return ResponseEntity.ok(ApiResponse.success("Document published successfully", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to publish document", e.getMessage()));
        }
    }

    /**
     * 获取文档审批历史
     */
    @GetMapping("/{documentId}/history")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getApprovalHistory(
            @PathVariable Long documentId) {
        try {
            Document document = documentService.getDocumentById(documentId)
                    .orElseThrow(() -> new RuntimeException("Document not found"));

            // 创建审批历史记录
            List<Map<String, Object>> history = List.of(
                Map.of(
                    "action", "创建",
                    "status", "DRAFT",
                    "timestamp", document.getCreatedAt().toString(),
                    "user", Map.of(
                        "username", document.getOwner().getUsername(),
                        "fullName", document.getOwner().getFullName()
                    ),
                    "notes", "文档创建"
                )
            );

            // 如果有审核日期，添加提交审核记录
            if (document.getReviewDate() != null) {
                Map<String, Object> reviewRecord = new HashMap<>();
                reviewRecord.put("action", "提交审核");
                reviewRecord.put("status", "UNDER_REVIEW");
                reviewRecord.put("timestamp", document.getReviewDate().toString());
                reviewRecord.put("user", Map.of(
                    "username", document.getOwner().getUsername(),
                    "fullName", document.getOwner().getFullName()
                ));
                reviewRecord.put("notes", "提交审核");
                history.add(reviewRecord);
            }

            // 如果有批准日期，添加批准记录
            if (document.getApprovalDate() != null && document.getApprovedBy() != null) {
                Map<String, Object> approvalRecord = new HashMap<>();
                approvalRecord.put("action", "批准");
                approvalRecord.put("status", document.getStatus().toString());
                approvalRecord.put("timestamp", document.getApprovalDate().toString());
                approvalRecord.put("user", Map.of(
                    "username", document.getApprovedBy().getUsername(),
                    "fullName", document.getApprovedBy().getFullName()
                ));
                approvalRecord.put("notes", "文档已批准");
                history.add(approvalRecord);
            }

            return ResponseEntity.ok(ApiResponse.success("Approval history retrieved", history));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to retrieve approval history", e.getMessage()));
        }
    }
}
