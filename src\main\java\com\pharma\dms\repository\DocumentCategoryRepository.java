package com.pharma.dms.repository;

import com.pharma.dms.entity.DocumentCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentCategoryRepository extends JpaRepository<DocumentCategory, Long> {

    Optional<DocumentCategory> findByName(String name);
    
    Optional<DocumentCategory> findByCode(String code);
    
    Boolean existsByName(String name);
    
    Boolean existsByCode(String code);
    
    List<DocumentCategory> findByIsActiveTrue();
    
    List<DocumentCategory> findByIsActiveFalse();
    
    @Query("SELECT c FROM DocumentCategory c WHERE c.parentCategory IS NULL ORDER BY c.sortOrder, c.name")
    List<DocumentCategory> findRootCategories();
    
    @Query("SELECT c FROM DocumentCategory c WHERE c.parentCategory.id = :parentId ORDER BY c.sortOrder, c.name")
    List<DocumentCategory> findByParentCategoryId(@Param("parentId") Long parentId);
    
    @Query("SELECT c FROM DocumentCategory c ORDER BY c.sortOrder, c.name")
    List<DocumentCategory> findAllOrderBySortOrder();
    
    @Query("SELECT c FROM DocumentCategory c WHERE c.isActive = true ORDER BY c.sortOrder, c.name")
    List<DocumentCategory> findActiveOrderBySortOrder();
    
    @Query("SELECT COUNT(d) FROM Document d WHERE d.category.id = :categoryId")
    Long countDocumentsByCategoryId(@Param("categoryId") Long categoryId);
}
