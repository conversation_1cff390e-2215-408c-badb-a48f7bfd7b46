<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>培训课程 - 制药文档管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" style="min-height: 100vh; background-color: #f8f9fa; border-right: 1px solid #dee2e6;">
                <div class="position-sticky pt-3">
                    <div class="navbar-brand mb-4" style="font-weight: bold; color: #0d6efd !important;">
                        <i class="fas fa-pills me-2"></i>
                        制药文档管理系统
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/users" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/documents" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-file-alt me-2"></i>
                                文档管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/system-overview" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-line me-2"></i>
                                系统概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/my-training" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user-graduate me-2"></i>
                                我的培训
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/training-courses" style="background-color: #0d6efd; color: white !important; border-radius: 0.375rem;">
                                <i class="fas fa-graduation-cap me-2"></i>
                                培训课程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/reports" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-bar me-2"></i>
                                报表管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/settings" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-cog me-2"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>

                    <hr>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/profile" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user me-2"></i>
                                个人资料
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">培训课程</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button class="btn btn-success" onclick="showMyCourses()">
                                <i class="fas fa-user me-1"></i>我的课程
                            </button>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCourseModal">
                                <i class="fas fa-plus me-1"></i>创建课程
                            </button>
                        </div>
                        <div class="btn-group me-2">
                            <button class="btn btn-outline-info" onclick="importCourses()" title="导入课程">
                                <i class="fas fa-file-import me-1"></i>导入
                            </button>
                            <button class="btn btn-outline-warning" onclick="exportCourses()" title="导出课程">
                                <i class="fas fa-file-export me-1"></i>导出
                            </button>
                            <button class="btn btn-outline-secondary" onclick="showCourseTemplates()" title="课程模板">
                                <i class="fas fa-layer-group me-1"></i>模板
                            </button>
                        </div>
                        <div class="btn-group me-2">
                            <button class="btn btn-outline-primary" onclick="showTrainingCalendar()" title="培训日历">
                                <i class="fas fa-calendar-alt me-1"></i>日历
                            </button>
                            <button class="btn btn-outline-success" onclick="showTrainingReports()" title="培训报表">
                                <i class="fas fa-chart-line me-1"></i>报表
                            </button>
                            <button class="btn btn-outline-dark" onclick="showTrainingAnalytics()" title="培训分析">
                                <i class="fas fa-analytics me-1"></i>分析
                            </button>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary" onclick="refreshCourses()" title="刷新列表">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" title="更多操作">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="bulkAssignCourses()"><i class="fas fa-users me-2"></i>批量分配</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="archiveOldCourses()"><i class="fas fa-archive me-2"></i>归档旧课程</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="duplicateCourse()"><i class="fas fa-copy me-2"></i>复制课程</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="showCourseSettings()"><i class="fas fa-cog me-2"></i>课程设置</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Page content -->

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    课程总数</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalCourses">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-graduation-cap fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    活跃课程</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeCourses">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-play fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    必修课程</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="mandatoryCourses">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Awaiting Approval</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="awaitingApproval">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Search & Filter</h6>
            </div>
            <div class="card-body">
                <form id="searchForm" class="row g-3">
                    <div class="col-md-3">
                        <label for="searchTitle" class="form-label">课程标题</label>
                        <input type="text" class="form-control" id="searchTitle" placeholder="按标题搜索">
                    </div>
                    <div class="col-md-3">
                        <label for="searchCourseCode" class="form-label">课程代码</label>
                        <input type="text" class="form-control" id="searchCourseCode" placeholder="课程代码">
                    </div>
                    <div class="col-md-3">
                        <label for="searchType" class="form-label">课程类型</label>
                        <select class="form-select" id="searchType">
                            <option value="">所有类型</option>
                            <option value="GENERAL">通用培训</option>
                            <option value="GMP">GMP培训</option>
                            <option value="SOP">SOP培训</option>
                            <option value="SAFETY">安全培训</option>
                            <option value="QUALITY">质量培训</option>
                            <option value="REGULATORY">法规培训</option>
                            <option value="TECHNICAL">技术培训</option>
                            <option value="COMPLIANCE">合规培训</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="searchStatus" class="form-label">状态</label>
                        <select class="form-select" id="searchStatus">
                            <option value="">所有状态</option>
                            <option value="DRAFT">草稿</option>
                            <option value="UNDER_REVIEW">审核中</option>
                            <option value="APPROVED">已批准</option>
                            <option value="ACTIVE">活跃</option>
                            <option value="INACTIVE">非活跃</option>
                            <option value="ARCHIVED">已归档</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>搜索
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="clearSearch()">
                            <i class="fas fa-times me-2"></i>清除
                        </button>
                        <button type="button" class="btn btn-info ms-2" onclick="showActiveCourses()">
                            <i class="fas fa-filter me-2"></i>仅活跃课程
                        </button>
                        <button type="button" class="btn btn-warning ms-2" onclick="showMandatoryCourses()">
                            <i class="fas fa-star me-2"></i>仅必修课程
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Courses Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">培训课程</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="coursesTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>课程代码</th>
                                <th>课程标题</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>时长</th>
                                <th>及格分数</th>
                                <th>必修</th>
                                <th>讲师</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="coursesTableBody">
                            <!-- Courses will be loaded here -->
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <nav aria-label="Courses pagination">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination will be generated here -->
                    </ul>
                </nav>
            </div>
        </div>

        <!-- Create Course Modal -->
        <div class="modal fade" id="createCourseModal" tabindex="-1" aria-labelledby="createCourseModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="createCourseModalLabel">
                            <i class="fas fa-robot me-2 text-primary"></i>AI智能创建培训课程
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                    </div>
                    <form id="createCourseForm">
                        <div class="modal-body">
                            <!-- AI功能提示 -->
                            <div class="alert alert-info mb-4">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>AI智能生成功能：</strong>
                                上传PDF、Word等文档，AI将自动生成培训大纲、详细内容和考试题目！
                                <small class="d-block mt-1">
                                    <i class="fas fa-brain me-1"></i>
                                    使用 <strong>DeepSeek R1 0528 (free)</strong> 模型，专为制药行业培训优化
                                </small>
                            </div>

                            <!-- 文档上传区域 -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-upload me-2"></i>上传培训文档 (可选)
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="trainingDocument" class="form-label">选择文档文件</label>
                                        <input type="file" class="form-control" id="trainingDocument"
                                               accept=".pdf,.doc,.docx,.txt" onchange="handleFileUpload(this)">
                                        <div class="form-text">
                                            支持格式：PDF, Word (.doc/.docx), 文本文件 (.txt)
                                        </div>
                                    </div>
                                    <div id="fileUploadStatus" class="d-none">
                                        <div class="alert alert-success">
                                            <i class="fas fa-check-circle me-2"></i>
                                            文件已选择：<span id="selectedFileName"></span>
                                            <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="generateAIContent()">
                                                <i class="fas fa-magic me-1"></i>AI生成内容
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="courseCode" class="form-label">课程代码 *</label>
                                        <input type="text" class="form-control" id="courseCode" name="courseCode" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="courseTitle" class="form-label">课程标题 *</label>
                                        <input type="text" class="form-control" id="courseTitle" name="title" required>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="courseDescription" class="form-label">课程描述</label>
                                <textarea class="form-control" id="courseDescription" name="description" rows="3"></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="courseType" class="form-label">课程类型 *</label>
                                        <select class="form-select" id="courseType" name="courseType" required>
                                            <option value="GENERAL">通用培训</option>
                                            <option value="GMP">GMP培训</option>
                                            <option value="SOP">SOP培训</option>
                                            <option value="SAFETY">安全培训</option>
                                            <option value="QUALITY">质量培训</option>
                                            <option value="REGULATORY">法规培训</option>
                                            <option value="TECHNICAL">技术培训</option>
                                            <option value="COMPLIANCE">合规培训</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="durationMinutes" class="form-label">时长（分钟）</label>
                                        <input type="number" class="form-control" id="durationMinutes" name="durationMinutes" min="1">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="passingScore" class="form-label">及格分数（%）</label>
                                        <input type="number" class="form-control" id="passingScore" name="passingScore" value="80" min="0" max="100">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="maxAttempts" class="form-label">最大尝试次数</label>
                                        <input type="number" class="form-control" id="maxAttempts" name="maxAttempts" value="3" min="1">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="validityPeriodMonths" class="form-label">有效期（月）</label>
                                        <input type="number" class="form-control" id="validityPeriodMonths" name="validityPeriodMonths" min="1">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3 d-flex align-items-end">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="isMandatory" name="isMandatory">
                                            <label class="form-check-label" for="isMandatory">
                                                必修课程
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="learningObjectives" class="form-label">学习目标</label>
                                <textarea class="form-control" id="learningObjectives" name="learningObjectives" rows="2"></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="prerequisites" class="form-label">前置要求</label>
                                <textarea class="form-control" id="prerequisites" name="prerequisites" rows="2"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>创建课程
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Course Details Modal -->
        <div class="modal fade" id="courseDetailsModal" tabindex="-1" aria-labelledby="courseDetailsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="courseDetailsModalLabel">Course Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="courseDetailsContent">
                        <!-- Course details will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 0;
        let currentSize = 10;
        let currentSort = 'createdAt';
        let currentDir = 'desc';

        document.addEventListener('DOMContentLoaded', function() {
            loadCourses();
            loadCourseStats();
        });

        async function loadCourses() {
            try {
                const searchParams = new URLSearchParams({
                    page: currentPage,
                    size: currentSize,
                    sortBy: currentSort,
                    sortDir: currentDir
                });

                // Add search filters
                const title = document.getElementById('searchTitle').value;
                const courseCode = document.getElementById('searchCourseCode').value;
                const courseType = document.getElementById('searchType').value;
                const status = document.getElementById('searchStatus').value;

                if (title) searchParams.append('title', title);
                if (courseCode) searchParams.append('courseCode', courseCode);
                if (courseType) searchParams.append('courseType', courseType);
                if (status) searchParams.append('status', status);

                const response = await authUtils.secureApiCall(`/dms/api/training-courses?${searchParams}`);

                if (response.ok) {
                    const result = await response.json();
                    displayCourses(result.data.content);
                    updatePagination(result.data);
                } else {
                    console.error('Failed to load courses');
                }
            } catch (error) {
                console.error('Error loading courses:', error);
            }
        }

        async function loadCourseStats() {
            try {
                const response = await authUtils.secureApiCall('/dms/api/training-courses/stats');

                if (response.ok) {
                    const result = await response.json();
                    const stats = result.data;
                    
                    // 显示实际统计数据
                    console.log('培训课程统计数据:', stats);
                    document.getElementById('totalCourses').textContent = stats.totalCourses || '0';
                    document.getElementById('activeCourses').textContent = stats.activeCourses || '0';
                    document.getElementById('mandatoryCourses').textContent = stats.mandatoryCourses || '0';
                    document.getElementById('awaitingApproval').textContent = stats.awaitingApproval || '0';
                    console.log('✅ 培训课程统计更新完成');
                }
            } catch (error) {
                console.error('Error loading course stats:', error);
            }
        }

        function displayCourses(courses) {
            const tbody = document.getElementById('coursesTableBody');
            tbody.innerHTML = '';

            courses.forEach(course => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <span class="badge bg-secondary">${course.courseCode}</span>
                    </td>
                    <td>
                        <div class="fw-bold">${course.title}</div>
                        <small class="text-muted">${course.description || ''}</small>
                    </td>
                    <td>
                        <span class="badge ${getCourseTypeBadgeClass(course.courseType)}">${getCourseTypeText(course.courseType)}</span>
                    </td>
                    <td>
                        <span class="badge ${getStatusBadgeClass(course.status)}">${getStatusText(course.status)}</span>
                    </td>
                    <td>${course.durationMinutes ? course.durationMinutes + ' 分钟' : '-'}</td>
                    <td>${course.passingScore}%</td>
                    <td>
                        ${course.isMandatory ? '<span class="badge bg-warning">必修</span>' : '<span class="badge bg-secondary">选修</span>'}
                    </td>
                    <td>${course.instructor ? course.instructor.firstName + ' ' + course.instructor.lastName : '-'}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewCourse(${course.id})" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="startTraining(${course.id})" title="开始培训">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="previewCourse(${course.id})" title="预览课程">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="btn-group ms-1" role="group">
                            <button class="btn btn-sm btn-outline-warning" onclick="assignCourse(${course.id})" title="分配培训">
                                <i class="fas fa-user-plus"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="duplicateCourse(${course.id})" title="复制课程">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-dark" onclick="editCourse(${course.id})" title="编辑课程">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                        <!-- 审批按钮 -->
                        <div class="btn-group ms-1" role="group">
                            ${course.status === 'DRAFT' ? `
                                <button class="btn btn-sm btn-outline-primary" onclick="submitCourseForApproval(${course.id})" title="提交审批">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            ` : ''}
                            ${course.status === 'UNDER_REVIEW' ? `
                                <button class="btn btn-sm btn-outline-success" onclick="approveCourse(${course.id})" title="批准课程">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning" onclick="rejectCourse(${course.id})" title="拒绝课程">
                                    <i class="fas fa-times"></i>
                                </button>
                            ` : ''}
                            ${course.status === 'APPROVED' ? `
                                <button class="btn btn-sm btn-outline-info" onclick="activateCourse(${course.id})" title="激活课程">
                                    <i class="fas fa-play"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Event handlers
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            currentPage = 0;
            loadCourses();
        });

        document.getElementById('createCourseForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const courseData = {
                courseCode: formData.get('courseCode'),
                title: formData.get('title'),
                description: formData.get('description'),
                courseType: formData.get('courseType'),
                durationMinutes: formData.get('durationMinutes') ? parseInt(formData.get('durationMinutes')) : 60,
                passingScore: formData.get('passingScore') ? parseInt(formData.get('passingScore')) : 80,
                maxAttempts: formData.get('maxAttempts') ? parseInt(formData.get('maxAttempts')) : 3,
                validityPeriodMonths: formData.get('validityPeriodMonths') ? parseInt(formData.get('validityPeriodMonths')) : null,
                isMandatory: formData.has('isMandatory'),
                learningObjectives: formData.get('learningObjectives'),
                prerequisites: formData.get('prerequisites')
            };

            console.log('=== 创建课程数据 ===');
            console.log('课程数据:', courseData);

            try {
                const response = await authUtils.secureApiCall('/dms/api/training-courses', {
                    method: 'POST',
                    body: JSON.stringify(courseData)
                });

                const result = await response.json();

                if (response.ok) {
                    alert('课程创建成功！');
                    bootstrap.Modal.getInstance(document.getElementById('createCourseModal')).hide();
                    this.reset();
                    loadCourses();
                    loadCourseStats();
                } else {
                    alert('创建课程失败：' + result.message);
                }
            } catch (error) {
                alert('创建课程失败：网络错误');
                console.error('Error:', error);
            }
        });

        // Utility functions
        function getCourseTypeBadgeClass(type) {
            switch (type) {
                case 'GMP': return 'bg-danger';
                case 'SOP': return 'bg-primary';
                case 'SAFETY': return 'bg-warning';
                case 'QUALITY': return 'bg-success';
                case 'REGULATORY': return 'bg-info';
                case 'TECHNICAL': return 'bg-dark';
                case 'COMPLIANCE': return 'bg-purple';
                default: return 'bg-secondary';
            }
        }

        function getStatusBadgeClass(status) {
            switch (status) {
                case 'DRAFT': return 'bg-secondary';
                case 'UNDER_REVIEW': return 'bg-warning';
                case 'APPROVED': return 'bg-info';
                case 'ACTIVE': return 'bg-success';
                case 'INACTIVE': return 'bg-dark';
                case 'ARCHIVED': return 'bg-danger';
                default: return 'bg-secondary';
            }
        }

        // 中文化函数
        function getCourseTypeText(courseType) {
            switch (courseType) {
                case 'GENERAL': return '通用培训';
                case 'GMP': return 'GMP培训';
                case 'SOP': return 'SOP培训';
                case 'SAFETY': return '安全培训';
                case 'QUALITY': return '质量培训';
                case 'REGULATORY': return '法规培训';
                case 'TECHNICAL': return '技术培训';
                case 'COMPLIANCE': return '合规培训';
                default: return courseType;
            }
        }

        function getStatusText(status) {
            switch (status) {
                case 'DRAFT': return '草稿';
                case 'UNDER_REVIEW': return '审核中';
                case 'APPROVED': return '已批准';
                case 'ACTIVE': return '已激活';
                case 'INACTIVE': return '未激活';
                case 'ARCHIVED': return '已归档';
                default: return status;
            }
        }

        function clearSearch() {
            document.getElementById('searchForm').reset();
            currentPage = 0;
            loadCourses();
        }

        function showActiveCourses() {
            document.getElementById('searchStatus').value = 'ACTIVE';
            currentPage = 0;
            loadCourses();
        }

        function showMandatoryCourses() {
            // This would need additional API endpoint
            alert('Mandatory courses filter will be implemented');
        }

        function showMyCourses() {
            // Navigate to my training page
            window.location.href = '/dms/my-training';
        }

        function viewCourse(id) {
            window.location.href = `/dms/course/${id}/details`;
        }

        function startTraining(id) {
            if (confirm('Start this training course?')) {
                window.location.href = `/dms/training/course/${id}/start`;
            }
        }

        function editCourse(id) {
            alert('Edit course functionality will be implemented');
        }

        function assignCourse(id) {
            // 跳转到培训分配页面
            window.location.href = `/dms/training-assignment?courseId=${id}`;
        }

        // 新增功能实现
        function previewCourse(id) {
            // 预览课程内容
            window.open(`/dms/course/${id}/preview`, '_blank');
        }

        function duplicateCourse(id) {
            if (confirm('确定要复制这个课程吗？')) {
                // 实现课程复制逻辑
                alert('课程复制功能开发中');
            }
        }

        function importCourses() {
            alert('课程导入功能开发中');
        }

        function exportCourses() {
            alert('课程导出功能开发中');
        }

        function showCourseTemplates() {
            alert('课程模板功能开发中');
        }

        function showTrainingCalendar() {
            alert('培训日历功能开发中');
        }

        function showTrainingReports() {
            alert('培训报表功能开发中');
        }

        function showTrainingAnalytics() {
            alert('培训分析功能开发中');
        }

        function refreshCourses() {
            loadCourses();
            loadCourseStats();
            alert('课程列表已刷新');
        }

        function bulkAssignCourses() {
            alert('批量分配功能开发中');
        }

        function archiveOldCourses() {
            if (confirm('确定要归档旧课程吗？')) {
                alert('归档功能开发中');
            }
        }

        function showCourseSettings() {
            alert('课程设置功能开发中');
        }

        function updatePagination(pageData) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            for (let i = 0; i < pageData.totalPages; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i + 1}</a>`;
                pagination.appendChild(li);
            }
        }

        function changePage(page) {
            currentPage = page;
            loadCourses();
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }

        // AI功能相关函数
        let selectedFile = null;
        let aiGeneratedContent = {
            outline: '',
            content: '',
            questions: ''
        };

        function handleFileUpload(input) {
            if (input.files && input.files[0]) {
                selectedFile = input.files[0];
                document.getElementById('selectedFileName').textContent = selectedFile.name;
                document.getElementById('fileUploadStatus').classList.remove('d-none');

                console.log('文件已选择:', selectedFile.name);
            }
        }

        async function generateAIContent() {
            if (!selectedFile) {
                alert('请先选择文件');
                return;
            }

            const courseTitle = document.getElementById('courseTitle').value;
            if (!courseTitle.trim()) {
                alert('请先输入课程标题');
                document.getElementById('courseTitle').focus();
                return;
            }

            try {
                // 显示加载状态
                const generateBtn = document.querySelector('[onclick="generateAIContent()"]');
                const originalText = generateBtn.innerHTML;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>AI生成中...';
                generateBtn.disabled = true;

                console.log('=== 开始AI生成内容 ===');
                console.log('文件:', selectedFile.name);
                console.log('课程标题:', courseTitle);

                // 创建FormData
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('courseTitle', courseTitle);

                // 调用AI生成大纲
                const response = await authUtils.secureApiCall('/dms/api/training-ai/generate-outline', {
                    method: 'POST',
                    body: formData,
                    headers: {} // 让浏览器自动设置Content-Type
                });

                if (response.ok) {
                    const result = await response.json();
                    aiGeneratedContent.outline = result.data.outline;

                    console.log('✅ AI大纲生成成功');

                    // 显示生成结果
                    showAIGeneratedContent(result.data);

                    // 自动填充表单
                    fillFormWithAIContent(result.data);

                } else {
                    const error = await response.json();
                    throw new Error(error.message || 'AI生成失败');
                }

                // 恢复按钮状态
                generateBtn.innerHTML = originalText;
                generateBtn.disabled = false;

            } catch (error) {
                console.error('❌ AI生成失败:', error);
                alert('AI生成失败: ' + error.message);

                // 恢复按钮状态
                const generateBtn = document.querySelector('[onclick="generateAIContent()"]');
                generateBtn.innerHTML = '<i class="fas fa-magic me-1"></i>AI生成内容';
                generateBtn.disabled = false;
            }
        }

        function showAIGeneratedContent(data) {
            // 创建显示AI生成内容的模态框
            const aiModal = document.createElement('div');
            aiModal.className = 'modal fade';
            aiModal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-robot me-2 text-primary"></i>AI生成的培训内容
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                AI已成功分析文档 <strong>${data.fileName}</strong> 并生成培训大纲！
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-list-ul me-2"></i>培训大纲
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <pre style="white-space: pre-wrap; font-family: inherit;">${data.outline}</pre>
                                </div>
                            </div>

                            <div class="mt-3">
                                <button class="btn btn-primary me-2" onclick="generateDetailedContent()">
                                    <i class="fas fa-file-alt me-1"></i>生成详细内容
                                </button>
                                <button class="btn btn-success me-2" onclick="generateExamQuestions()">
                                    <i class="fas fa-question-circle me-1"></i>生成考试题目
                                </button>
                                <button class="btn btn-info" onclick="testAIConnection()">
                                    <i class="fas fa-plug me-1"></i>测试AI连接
                                </button>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" onclick="applyAIContent()" data-bs-dismiss="modal">
                                <i class="fas fa-check me-1"></i>应用到课程
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(aiModal);
            const modal = new bootstrap.Modal(aiModal);
            modal.show();

            aiModal.addEventListener('hidden.bs.modal', () => {
                aiModal.remove();
            });
        }

        function fillFormWithAIContent(data) {
            // 自动填充课程描述
            if (data.outline) {
                const description = data.outline.substring(0, 200) + '...';
                document.getElementById('courseDescription').value = description;
            }
        }

        async function generateDetailedContent() {
            if (!aiGeneratedContent.outline) {
                alert('请先生成培训大纲');
                return;
            }

            try {
                const courseTitle = document.getElementById('courseTitle').value;

                const response = await authUtils.secureApiCall('/dms/api/training-ai/generate-content', {
                    method: 'POST',
                    body: JSON.stringify({
                        outline: aiGeneratedContent.outline,
                        courseTitle: courseTitle
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    aiGeneratedContent.content = result.data.content;
                    alert('详细内容生成成功！');
                    console.log('详细内容:', result.data.content);
                } else {
                    throw new Error('生成详细内容失败');
                }
            } catch (error) {
                alert('生成详细内容失败: ' + error.message);
            }
        }

        async function generateExamQuestions() {
            if (!aiGeneratedContent.content && !aiGeneratedContent.outline) {
                alert('请先生成培训内容');
                return;
            }

            try {
                const courseTitle = document.getElementById('courseTitle').value;
                const content = aiGeneratedContent.content || aiGeneratedContent.outline;

                const response = await authUtils.secureApiCall('/dms/api/training-ai/generate-questions', {
                    method: 'POST',
                    body: JSON.stringify({
                        courseContent: content,
                        courseTitle: courseTitle,
                        questionCount: 10
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    aiGeneratedContent.questions = result.data.questions;
                    alert('考试题目生成成功！');
                    console.log('考试题目:', result.data.questions);
                } else {
                    throw new Error('生成考试题目失败');
                }
            } catch (error) {
                alert('生成考试题目失败: ' + error.message);
            }
        }

        async function testAIConnection() {
            try {
                const response = await authUtils.secureApiCall('/dms/api/training-ai/test-connection');

                if (response.ok) {
                    const result = await response.json();
                    if (result.data.connected) {
                        alert('AI连接正常！\n模型: ' + result.data.model);
                    } else {
                        alert('AI连接失败: ' + result.data.error);
                    }
                } else {
                    alert('AI连接测试失败');
                }
            } catch (error) {
                alert('AI连接测试异常: ' + error.message);
            }
        }

        function applyAIContent() {
            // 应用AI生成的内容到表单
            if (aiGeneratedContent.outline) {
                const learningObjectives = document.getElementById('learningObjectives');
                learningObjectives.value = '基于AI分析生成的学习目标';
            }

            alert('AI内容已应用到课程表单！');
        }

        // 培训课程审批相关函数
        async function submitCourseForApproval(courseId) {
            if (!confirm('确定要提交此课程进行审批吗？')) return;

            try {
                console.log('=== 提交课程审批 ===');
                console.log('课程ID:', courseId);

                const response = await authUtils.secureApiCall(`/dms/api/training-courses/${courseId}/submit-approval`, {
                    method: 'POST'
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('课程已提交审批！');
                    loadCourses(); // 刷新课程列表
                    console.log('✅ 课程提交审批成功');
                } else {
                    const result = await response.json();
                    alert('提交审批失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                console.error('❌ 提交审批异常:', error);
                alert('提交审批失败: ' + error.message);
            }
        }

        async function approveCourse(courseId) {
            if (!confirm('确定要批准此课程吗？')) return;

            try {
                console.log('=== 批准课程 ===');
                console.log('课程ID:', courseId);

                const response = await authUtils.secureApiCall(`/dms/api/training-courses/${courseId}/approve`, {
                    method: 'POST'
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('课程审批成功！');
                    loadCourses(); // 刷新课程列表
                    console.log('✅ 课程审批成功');
                } else {
                    const result = await response.json();
                    alert('审批失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                console.error('❌ 审批异常:', error);
                alert('审批失败: ' + error.message);
            }
        }

        async function rejectCourse(courseId) {
            const reason = prompt('请输入拒绝原因:');
            if (!reason || reason.trim() === '') {
                alert('请输入拒绝原因');
                return;
            }

            try {
                console.log('=== 拒绝课程 ===');
                console.log('课程ID:', courseId);

                const response = await authUtils.secureApiCall(`/dms/api/training-courses/${courseId}/reject`, {
                    method: 'POST',
                    body: JSON.stringify({ rejectionReason: reason })
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('课程已拒绝！');
                    loadCourses(); // 刷新课程列表
                    console.log('✅ 课程拒绝成功');
                } else {
                    const result = await response.json();
                    alert('拒绝失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                console.error('❌ 拒绝异常:', error);
                alert('拒绝失败: ' + error.message);
            }
        }

        async function activateCourse(courseId) {
            if (!confirm('确定要激活此课程吗？激活后课程将对学员开放。')) return;

            try {
                console.log('=== 激活课程 ===');
                console.log('课程ID:', courseId);

                const response = await authUtils.secureApiCall(`/dms/api/training-courses/${courseId}/activate`, {
                    method: 'POST'
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('课程激活成功！');
                    loadCourses(); // 刷新课程列表
                    console.log('✅ 课程激活成功');
                } else {
                    const result = await response.json();
                    alert('激活失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                console.error('❌ 激活异常:', error);
                alert('激活失败: ' + error.message);
            }
        }
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 认证工具 -->
    <script src="/dms/js/auth.js"></script>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
