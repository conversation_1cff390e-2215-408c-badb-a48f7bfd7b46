package com.pharma.dms.repository;

import com.pharma.dms.entity.Document;
import com.pharma.dms.entity.DocumentPermission;
import com.pharma.dms.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentPermissionRepository extends JpaRepository<DocumentPermission, Long> {

    Optional<DocumentPermission> findByDocumentAndUser(Document document, User user);
    
    List<DocumentPermission> findByDocument(Document document);
    
    List<DocumentPermission> findByUser(User user);
    
    List<DocumentPermission> findByDocumentAndIsActiveTrue(Document document);
    
    List<DocumentPermission> findByUserAndIsActiveTrue(User user);
    
    @Query("SELECT dp FROM DocumentPermission dp WHERE dp.document.id = :documentId AND dp.user.id = :userId AND dp.isActive = true")
    Optional<DocumentPermission> findActivePermission(@Param("documentId") Long documentId, @Param("userId") Long userId);
    
    @Query("SELECT dp FROM DocumentPermission dp WHERE dp.user.id = :userId AND dp.permissionType = :permissionType AND dp.isActive = true")
    List<DocumentPermission> findByUserAndPermissionType(@Param("userId") Long userId, 
                                                         @Param("permissionType") DocumentPermission.PermissionType permissionType);
    
    @Query("SELECT dp FROM DocumentPermission dp WHERE dp.expiresAt < :now AND dp.isActive = true")
    List<DocumentPermission> findExpiredPermissions(@Param("now") LocalDateTime now);
    
    @Query("SELECT dp FROM DocumentPermission dp WHERE dp.expiresAt BETWEEN :start AND :end AND dp.isActive = true")
    List<DocumentPermission> findExpiringPermissions(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);
    
    @Query("SELECT COUNT(dp) FROM DocumentPermission dp WHERE dp.document.id = :documentId AND dp.isActive = true")
    Long countActivePermissionsByDocument(@Param("documentId") Long documentId);
    
    @Query("SELECT COUNT(dp) FROM DocumentPermission dp WHERE dp.user.id = :userId AND dp.isActive = true")
    Long countActivePermissionsByUser(@Param("userId") Long userId);
}
