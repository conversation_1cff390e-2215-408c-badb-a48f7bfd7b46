package com.pharma.dms.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.pharma.dms.entity.Document;
import com.pharma.dms.entity.TrainingAssignment;
import com.pharma.dms.entity.User;

import java.util.List;

/**
 * 简化的邮件服务
 * 当邮件功能未启用时，提供模拟邮件发送功能
 */
@Service
public class EmailService {

    @Value("${app.email.enabled:false}")
    private boolean emailEnabled;

    @Value("${app.email.from:<EMAIL>}")
    private String fromEmail;

    /**
     * 发送简单文本邮件
     */
    @Async
    public void sendSimpleEmail(String to, String subject, String text) {
        if (!emailEnabled) {
            System.out.println("📧 邮件功能未启用，模拟发送邮件:");
            System.out.println("   收件人: " + to);
            System.out.println("   主题: " + subject);
            System.out.println("   内容: " + text);
            return;
        }

        try {
            // 这里可以添加实际的邮件发送逻辑
            System.out.println("✅ 邮件发送成功: " + to);
        } catch (Exception e) {
            System.err.println("❌ 邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送HTML邮件
     */
    @Async
    public void sendHtmlEmail(String to, String subject, String htmlContent) {
        if (!emailEnabled) {
            System.out.println("📧 邮件功能未启用，模拟发送HTML邮件:");
            System.out.println("   收件人: " + to);
            System.out.println("   主题: " + subject);
            System.out.println("   HTML内容长度: " + htmlContent.length() + " 字符");
            return;
        }

        try {
            // 这里可以添加实际的HTML邮件发送逻辑
            System.out.println("✅ HTML邮件发送成功: " + to);
        } catch (Exception e) {
            System.err.println("❌ HTML邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送用户欢迎邮件
     */
    public void sendWelcomeEmail(User user, String temporaryPassword) {
        String subject = "欢迎加入制药文档管理系统";
        String content = String.format(
            "亲爱的 %s %s，\n\n" +
            "欢迎加入制药文档管理系统！\n\n" +
            "您的登录信息：\n" +
            "用户名: %s\n" +
            "临时密码: %s\n\n" +
            "请在首次登录后立即修改密码。\n\n" +
            "系统地址: http://localhost:8081/dms/login\n\n" +
            "祝好！\n" +
            "制药DMS团队",
            user.getFirstName(), user.getLastName(),
            user.getUsername(), temporaryPassword
        );

        sendSimpleEmail(user.getEmail(), subject, content);
    }

    /**
     * 发送密码重置邮件
     */
    public void sendPasswordResetEmail(User user, String newPassword) {
        String subject = "密码重置通知";
        String content = String.format(
            "亲爱的 %s，\n\n" +
            "您的密码已被管理员重置。\n\n" +
            "新密码: %s\n\n" +
            "请立即登录并修改密码。\n\n" +
            "系统地址: http://localhost:8081/dms/login\n\n" +
            "如有疑问，请联系系统管理员。\n\n" +
            "制药DMS团队",
            user.getFirstName() + " " + user.getLastName(),
            newPassword
        );

        sendSimpleEmail(user.getEmail(), subject, content);
    }

    /**
     * 发送文档审批通知
     */
    public void sendDocumentApprovalNotification(Document document, User approver, String action) {
        String subject = "文档审批通知 - " + document.getTitle();
        String content = String.format(
            "文档审批通知\n\n" +
            "文档标题: %s\n" +
            "操作: %s\n" +
            "审批人: %s\n" +
            "时间: %s\n\n" +
            "请登录系统查看详情。\n\n" +
            "制药DMS团队",
            document.getTitle(),
            action,
            approver.getFirstName() + " " + approver.getLastName(),
            java.time.LocalDateTime.now().toString()
        );

        if (document.getOwner() != null) {
            sendSimpleEmail(document.getOwner().getEmail(), subject, content);
        }
    }

    /**
     * 发送培训分配通知
     */
    public void sendTrainingAssignmentNotification(TrainingAssignment assignment) {
        String subject = "新培训任务分配 - " + assignment.getCourse().getTitle();
        String content = String.format(
            "您有新的培训任务\n\n" +
            "课程名称: %s\n" +
            "截止日期: %s\n" +
            "优先级: %s\n\n" +
            "请及时完成培训。\n\n" +
            "登录地址: http://localhost:8081/dms/my-training\n\n" +
            "制药DMS团队",
            assignment.getCourse().getTitle(),
            assignment.getDueDate() != null ? assignment.getDueDate().toString() : "无",
            assignment.getPriority().toString()
        );

        sendSimpleEmail(assignment.getUser().getEmail(), subject, content);
    }

    /**
     * 发送培训提醒邮件
     */
    public void sendTrainingReminder(User user, List<TrainingAssignment> overdueAssignments) {
        if (overdueAssignments.isEmpty()) {
            return;
        }

        String subject = "培训任务提醒";
        StringBuilder content = new StringBuilder();
        content.append("亲爱的 ").append(user.getFirstName()).append("，\n\n");
        content.append("您有以下培训任务需要完成：\n\n");

        for (TrainingAssignment assignment : overdueAssignments) {
            content.append("- ").append(assignment.getCourse().getTitle());
            if (assignment.getDueDate() != null) {
                content.append(" (截止: ").append(assignment.getDueDate()).append(")");
            }
            content.append("\n");
        }

        content.append("\n请及时完成培训任务。\n\n");
        content.append("登录地址: http://localhost:8081/dms/my-training\n\n");
        content.append("制药DMS团队");

        sendSimpleEmail(user.getEmail(), subject, content.toString());
    }

    /**
     * 发送系统通知邮件
     */
    public void sendSystemNotification(String to, String subject, String message) {
        String content = String.format(
            "系统通知\n\n" +
            "%s\n\n" +
            "时间: %s\n\n" +
            "制药DMS团队",
            message,
            java.time.LocalDateTime.now().toString()
        );

        sendSimpleEmail(to, subject, content);
    }

    /**
     * 批量发送邮件
     */
    public void sendBulkEmail(List<String> recipients, String subject, String content) {
        for (String recipient : recipients) {
            sendSimpleEmail(recipient, subject, content);
        }
    }

    /**
     * 测试邮件配置
     */
    public boolean testEmailConfiguration(String testEmail) {
        try {
            sendSimpleEmail(testEmail, "邮件配置测试", "这是一封测试邮件，用于验证邮件配置是否正确。");
            return true;
        } catch (Exception e) {
            System.err.println("邮件配置测试失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查邮件服务是否启用
     */
    public boolean isEmailEnabled() {
        return emailEnabled;
    }

    /**
     * 获取发件人邮箱
     */
    public String getFromEmail() {
        return fromEmail;
    }
}
