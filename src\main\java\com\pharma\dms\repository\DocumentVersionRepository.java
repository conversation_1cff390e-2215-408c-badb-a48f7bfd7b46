package com.pharma.dms.repository;

import com.pharma.dms.entity.Document;
import com.pharma.dms.entity.DocumentVersion;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentVersionRepository extends JpaRepository<DocumentVersion, Long> {
    
    // 基本查询
    List<DocumentVersion> findByDocumentOrderByCreatedAtDesc(Document document);
    
    List<DocumentVersion> findByDocumentIdOrderByCreatedAtDesc(Long documentId);
    
    Optional<DocumentVersion> findByDocumentAndIsCurrent(Document document, Boolean isCurrent);
    
    Optional<DocumentVersion> findByDocumentIdAndIsCurrent(Long documentId, Boolean isCurrent);
    
    Optional<DocumentVersion> findByDocumentAndVersionNumber(Document document, String versionNumber);
    
    Optional<DocumentVersion> findByDocumentIdAndVersionNumber(Long documentId, String versionNumber);
    
    // 状态查询
    List<DocumentVersion> findByStatus(DocumentVersion.VersionStatus status);
    
    List<DocumentVersion> findByDocumentAndStatus(Document document, DocumentVersion.VersionStatus status);
    
    List<DocumentVersion> findByDocumentIdAndStatus(Long documentId, DocumentVersion.VersionStatus status);
    
    // 变更类型查询
    List<DocumentVersion> findByChangeType(DocumentVersion.ChangeType changeType);
    
    List<DocumentVersion> findByDocumentAndChangeType(Document document, DocumentVersion.ChangeType changeType);
    
    // 创建者查询
    List<DocumentVersion> findByCreatedByIdOrderByCreatedAtDesc(Long createdById);
    
    List<DocumentVersion> findByDocumentAndCreatedByIdOrderByCreatedAtDesc(Document document, Long createdById);
    
    // 审批者查询
    List<DocumentVersion> findByApprovedByIdOrderByApprovedAtDesc(Long approvedById);
    
    // 时间范围查询
    List<DocumentVersion> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<DocumentVersion> findByDocumentAndCreatedAtBetween(Document document, LocalDateTime startDate, LocalDateTime endDate);
    
    List<DocumentVersion> findByApprovedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<DocumentVersion> findByEffectiveDateBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    // 归档和保留查询
    List<DocumentVersion> findByIsArchived(Boolean isArchived);
    
    List<DocumentVersion> findByRetentionDateBefore(LocalDateTime date);
    
    @Query("SELECT dv FROM DocumentVersion dv WHERE dv.retentionDate IS NOT NULL AND dv.retentionDate <= :date")
    List<DocumentVersion> findVersionsReadyForDeletion(@Param("date") LocalDateTime date);
    
    // 版本比较查询
    @Query("SELECT dv FROM DocumentVersion dv WHERE dv.document = :document AND " +
           "(dv.majorVersion > :majorVersion OR " +
           "(dv.majorVersion = :majorVersion AND dv.minorVersion > :minorVersion) OR " +
           "(dv.majorVersion = :majorVersion AND dv.minorVersion = :minorVersion AND dv.patchVersion > :patchVersion))")
    List<DocumentVersion> findNewerVersions(@Param("document") Document document,
                                          @Param("majorVersion") Integer majorVersion,
                                          @Param("minorVersion") Integer minorVersion,
                                          @Param("patchVersion") Integer patchVersion);
    
    @Query("SELECT dv FROM DocumentVersion dv WHERE dv.document = :document AND " +
           "(dv.majorVersion < :majorVersion OR " +
           "(dv.majorVersion = :majorVersion AND dv.minorVersion < :minorVersion) OR " +
           "(dv.majorVersion = :majorVersion AND dv.minorVersion = :minorVersion AND dv.patchVersion < :patchVersion))")
    List<DocumentVersion> findOlderVersions(@Param("document") Document document,
                                          @Param("majorVersion") Integer majorVersion,
                                          @Param("minorVersion") Integer minorVersion,
                                          @Param("patchVersion") Integer patchVersion);
    
    // 最新版本查询
    @Query("SELECT dv FROM DocumentVersion dv WHERE dv.document = :document AND " +
           "dv.majorVersion = (SELECT MAX(dv2.majorVersion) FROM DocumentVersion dv2 WHERE dv2.document = :document) AND " +
           "dv.minorVersion = (SELECT MAX(dv3.minorVersion) FROM DocumentVersion dv3 WHERE dv3.document = :document AND dv3.majorVersion = dv.majorVersion) AND " +
           "dv.patchVersion = (SELECT MAX(dv4.patchVersion) FROM DocumentVersion dv4 WHERE dv4.document = :document AND dv4.majorVersion = dv.majorVersion AND dv4.minorVersion = dv.minorVersion)")
    Optional<DocumentVersion> findLatestVersion(@Param("document") Document document);
    
    @Query("SELECT dv FROM DocumentVersion dv WHERE dv.document.id = :documentId AND " +
           "dv.majorVersion = (SELECT MAX(dv2.majorVersion) FROM DocumentVersion dv2 WHERE dv2.document.id = :documentId) AND " +
           "dv.minorVersion = (SELECT MAX(dv3.minorVersion) FROM DocumentVersion dv3 WHERE dv3.document.id = :documentId AND dv3.majorVersion = dv.majorVersion) AND " +
           "dv.patchVersion = (SELECT MAX(dv4.patchVersion) FROM DocumentVersion dv4 WHERE dv4.document.id = :documentId AND dv4.majorVersion = dv.majorVersion AND dv4.minorVersion = dv.minorVersion)")
    Optional<DocumentVersion> findLatestVersionByDocumentId(@Param("documentId") Long documentId);
    
    // GMP相关查询
    List<DocumentVersion> findByTrainingRequired(Boolean trainingRequired);
    
    @Query("SELECT dv FROM DocumentVersion dv WHERE dv.gmpImpactAssessment IS NOT NULL AND dv.gmpImpactAssessment != ''")
    List<DocumentVersion> findVersionsWithGmpAssessment();
    
    @Query("SELECT dv FROM DocumentVersion dv WHERE dv.changeType IN ('MAJOR', 'MINOR') AND dv.status = 'ACTIVE'")
    List<DocumentVersion> findVersionsRequiringGmpReview();
    
    // 统计查询
    @Query("SELECT COUNT(dv) FROM DocumentVersion dv WHERE dv.document = :document")
    Long countVersionsByDocument(@Param("document") Document document);
    
    @Query("SELECT COUNT(dv) FROM DocumentVersion dv WHERE dv.document.id = :documentId")
    Long countVersionsByDocumentId(@Param("documentId") Long documentId);
    
    @Query("SELECT COUNT(dv) FROM DocumentVersion dv WHERE dv.status = :status")
    Long countByStatus(@Param("status") DocumentVersion.VersionStatus status);
    
    @Query("SELECT COUNT(dv) FROM DocumentVersion dv WHERE dv.changeType = :changeType")
    Long countByChangeType(@Param("changeType") DocumentVersion.ChangeType changeType);
    
    @Query("SELECT COUNT(dv) FROM DocumentVersion dv WHERE dv.createdBy.id = :userId")
    Long countByCreatedBy(@Param("userId") Long userId);
    
    @Query("SELECT COUNT(dv) FROM DocumentVersion dv WHERE dv.approvedBy.id = :userId")
    Long countByApprovedBy(@Param("userId") Long userId);
    
    // 分页查询
    Page<DocumentVersion> findByDocumentOrderByCreatedAtDesc(Document document, Pageable pageable);
    
    Page<DocumentVersion> findByDocumentIdOrderByCreatedAtDesc(Long documentId, Pageable pageable);
    
    Page<DocumentVersion> findByStatusOrderByCreatedAtDesc(DocumentVersion.VersionStatus status, Pageable pageable);
    
    Page<DocumentVersion> findByCreatedByIdOrderByCreatedAtDesc(Long createdById, Pageable pageable);
    
    // 复杂查询
    @Query("SELECT dv FROM DocumentVersion dv WHERE " +
           "(:documentId IS NULL OR dv.document.id = :documentId) AND " +
           "(:status IS NULL OR dv.status = :status) AND " +
           "(:changeType IS NULL OR dv.changeType = :changeType) AND " +
           "(:createdById IS NULL OR dv.createdBy.id = :createdById) AND " +
           "(:approvedById IS NULL OR dv.approvedBy.id = :approvedById) AND " +
           "(:startDate IS NULL OR dv.createdAt >= :startDate) AND " +
           "(:endDate IS NULL OR dv.createdAt <= :endDate) AND " +
           "(:isCurrent IS NULL OR dv.isCurrent = :isCurrent) AND " +
           "(:isArchived IS NULL OR dv.isArchived = :isArchived)")
    Page<DocumentVersion> findVersionsWithFilters(
            @Param("documentId") Long documentId,
            @Param("status") DocumentVersion.VersionStatus status,
            @Param("changeType") DocumentVersion.ChangeType changeType,
            @Param("createdById") Long createdById,
            @Param("approvedById") Long approvedById,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            @Param("isCurrent") Boolean isCurrent,
            @Param("isArchived") Boolean isArchived,
            Pageable pageable);
    
    // 审计和合规查询
    @Query("SELECT dv FROM DocumentVersion dv WHERE dv.document.id = :documentId AND " +
           "dv.createdAt BETWEEN :startDate AND :endDate ORDER BY dv.createdAt DESC")
    List<DocumentVersion> findVersionHistoryInPeriod(@Param("documentId") Long documentId,
                                                    @Param("startDate") LocalDateTime startDate,
                                                    @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT dv FROM DocumentVersion dv WHERE dv.status = 'ACTIVE' AND " +
           "dv.effectiveDate IS NOT NULL AND dv.effectiveDate <= :date AND " +
           "(dv.supersededDate IS NULL OR dv.supersededDate > :date)")
    List<DocumentVersion> findActiveVersionsOnDate(@Param("date") LocalDateTime date);
    
    @Query("SELECT dv FROM DocumentVersion dv WHERE dv.status IN ('UNDER_REVIEW', 'APPROVED') AND " +
           "dv.createdAt <= :deadline ORDER BY dv.createdAt ASC")
    List<DocumentVersion> findVersionsPendingActivation(@Param("deadline") LocalDateTime deadline);
    
    // 清理和维护查询
    @Query("SELECT dv FROM DocumentVersion dv WHERE dv.isArchived = false AND " +
           "dv.isCurrent = false AND dv.createdAt < :archiveDate")
    List<DocumentVersion> findVersionsEligibleForArchiving(@Param("archiveDate") LocalDateTime archiveDate);
    
    @Query("SELECT dv FROM DocumentVersion dv WHERE dv.downloadCount = 0 AND " +
           "dv.viewCount = 0 AND dv.createdAt < :unusedDate AND dv.isCurrent = false")
    List<DocumentVersion> findUnusedVersions(@Param("unusedDate") LocalDateTime unusedDate);
}
