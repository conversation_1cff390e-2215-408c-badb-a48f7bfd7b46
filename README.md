# Pharmaceutical Document Management System (DMS)

A comprehensive document management system designed specifically for the pharmaceutical industry with GMP compliance features.

## 🏗️ Architecture

### Backend
- **Spring Boot 3.2** - Main application framework
- **Spring Security 6** - Authentication and authorization
- **Spring Data JPA** - Data persistence layer
- **PostgreSQL 16.9** - Primary database
- **H2** - Development/testing database
- **JWT** - Token-based authentication
- **Thymeleaf** - Server-side templating

### Frontend (Planned)
- **Vue 3** - Primary frontend framework
- **React 18** - Alternative frontend option
- **Bootstrap 5** - UI components
- **RESTful APIs** - Backend communication

## 🚀 Getting Started

### Prerequisites
- Java 17 or higher
- PostgreSQL 16.9
- Maven 3.6+
- Git

### Environment Setup
Based on your system configuration:
- Java: `D:\JAVA\bin`
- PostgreSQL: `D:\sql\pgsql\bin`
- Node.js: `D:\nodejs\node-v22.16.0-win-x64`

### Database Setup

1. **Create Database**
   ```bash
   # Using PostgreSQL command line
   createdb pharma_dms
   ```

2. **Configure Database Connection**
   Update `src/main/resources/application.yml` if needed:
   ```yaml
   spring:
     datasource:
       url: *******************************************
       username: postgres
       password: your_password
   ```

### Running the Application

1. **Clone and Navigate**
   ```bash
   cd /d/learn/java/dms
   ```

2. **Run with Maven**

   **Option 1: Using Environment Variable (推荐)**
   ```bash
   # H2数据库模式 (开发测试)
   set SPRING_PROFILES_ACTIVE=h2
   mvn spring-boot:run

   # PostgreSQL数据库模式 (生产环境)
   set SPRING_PROFILES_ACTIVE=postgresql
   mvn spring-boot:run
   ```

   **Option 2: Using Startup Scripts**
   ```bash
   # H2模式
   start-h2.bat

   # PostgreSQL模式
   start-postgresql.bat
   ```

3. **Access the Application**
   - Web Interface: http://localhost:8081/dms/login
   - Network Access: http://**************:8081/dms/login
   - H2 Console (H2模式): http://localhost:8081/dms/h2-console

### Default Users

The system comes with pre-configured demo users:

| Username | Password | Role | Department |
|----------|----------|------|------------|
| admin | admin123 | Administrator | IT |
| qa_user | qa123 | Quality Assurance | QA |
| user | user123 | Regular User | R&D |

**Note**: Admin user requires password change on first login.

## 📋 Development Phases

### Phase 1: Foundation ✅
- [x] Spring Boot 3.2 + Spring Security 6 setup
- [x] PostgreSQL database integration
- [x] JWT authentication system
- [x] Basic user management
- [x] Thymeleaf templates
- [x] Role-based access control (Admin/QA/User)
- [x] Department management
- [x] Audit logging foundation

### Phase 2: Core Features (In Progress)
- [ ] User CRUD operations
- [ ] File management basics
- [ ] Dashboard with statistics
- [ ] User profile management
- [ ] Password management features

### Phase 3: Advanced Features (Planned)
- [ ] Complete audit logging
- [ ] File approval workflows
- [ ] OCR integration
- [ ] AI-powered features
- [ ] Vue.js/React frontend migration
- [ ] Advanced reporting

## 🔧 Configuration

### Application Properties
Key configuration files:
- `application.yml` - Main configuration
- `WebSecurityConfig.java` - Security settings
- `DataInitializer.java` - Initial data setup

### Database Profiles
- `postgresql` - Production database
- `h2` - Development/testing database

### JWT Configuration
```yaml
jwt:
  secret: your-secret-key
  expiration: 86400000  # 24 hours
```

## 🏢 GMP Compliance Features

### Implemented
- User authentication and authorization
- Role-based access control
- Basic audit logging
- Version control for entities
- Secure password handling

### Planned
- Electronic signatures
- Complete audit trails
- Document approval workflows
- Training management
- Compliance reporting

## 🔒 Security Features

- JWT-based authentication
- BCrypt password encryption
- Role-based authorization
- CORS configuration
- SQL injection prevention
- XSS protection

## 📊 Database Schema

### Core Tables
- `users` - User accounts and profiles
- `roles` - System roles (Admin, QA, User)
- `departments` - Organizational departments
- `user_roles` - User-role relationships
- `audit_logs` - System audit trail

## 🛠️ Development

### Project Structure
```
src/
├── main/
│   ├── java/com/pharma/dms/
│   │   ├── config/          # Configuration classes
│   │   ├── controller/      # REST and Web controllers
│   │   ├── dto/            # Data Transfer Objects
│   │   ├── entity/         # JPA entities
│   │   ├── repository/     # Data repositories
│   │   └── security/       # Security components
│   └── resources/
│       ├── templates/      # Thymeleaf templates
│       └── application.yml # Configuration
└── test/                   # Test classes
```

### API Endpoints

#### Authentication
- `POST /api/auth/signin` - User login
- `POST /api/auth/signup` - User registration

#### Web Pages
- `GET /login` - Login page
- `GET /dashboard` - Main dashboard
- `GET /users` - User management
- `GET /profile` - User profile

## 🚀 Deployment

### Local Development
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=postgresql
```

### Production Considerations
- Configure proper database credentials
- Set strong JWT secret
- Enable HTTPS
- Configure proper CORS origins
- Set up database backups
- Monitor application logs

## 📈 Performance Optimization

### Database
- Connection pooling (HikariCP)
- Proper indexing
- Query optimization
- Lazy loading for relationships

### Application
- Caching strategies
- Static resource optimization
- Compression
- Connection management

## 🔍 Monitoring and Logging

### Logging Levels
- Application: DEBUG
- Security: DEBUG
- SQL: DEBUG (development only)

### Audit Trail
All user actions are logged with:
- Username and timestamp
- Action performed
- Entity affected
- Old and new values
- IP address and user agent

## 🤝 Contributing

1. Follow the established code structure
2. Maintain GMP compliance requirements
3. Add appropriate tests
4. Update documentation
5. Follow security best practices

## 📞 Support

For technical support or questions about the pharmaceutical DMS system, please refer to the development team.

---

**Version**: 1.0.0  
**Last Updated**: December 2024  
**License**: Proprietary - Pharmaceutical Industry Use
