package com.pharma.dms.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.Document;
import com.pharma.dms.entity.User;
import com.pharma.dms.security.UserPrincipal;
import com.pharma.dms.service.DocumentService;
import com.pharma.dms.service.OCRService;
import com.pharma.dms.service.OCRServiceNew;
import com.pharma.dms.service.OpenRouterAIService;
import com.pharma.dms.service.UserService;

@RestController
@RequestMapping("/api/ai")
public class AIController {

    private static final Logger logger = LoggerFactory.getLogger(AIController.class);

    @Autowired
    private OpenRouterAIService openRouterAIService;

    @Autowired
    private OCRService ocrService;

    @Autowired
    @Qualifier("ocrServiceNew")
    private OCRServiceNew ocrServiceNew;

    @Autowired
    private UserService userService;

    @Autowired
    private DocumentService documentService;

    /**
     * OCR文本提取
     */
    @PostMapping("/ocr/extract-text")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<OCRServiceNew.OCRResult>> extractTextFromImage(
            @RequestParam("file") MultipartFile file) {
        
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文件为空", null));
            }

            // 检查文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("只支持图像文件", null));
            }

            // 尝试使用新的OCR服务，如果不可用则使用旧的
            OCRServiceNew.OCRResult result;
            if (ocrServiceNew.isOCREnabled()) {
                result = ocrServiceNew.extractTextFromImage(file.getBytes(), file.getOriginalFilename());
            } else {
                // 转换为旧的OCR结果格式
                OCRService.OCRResult oldResult = ocrService.extractTextFromImage(
                        file.getBytes(), file.getOriginalFilename());
                result = new OCRServiceNew.OCRResult(
                        oldResult.isSuccess(),
                        oldResult.getMessage(),
                        oldResult.getExtractedText(),
                        oldResult.getConfidence(),
                        oldResult.getKeywords()
                );
            }

            return ResponseEntity.ok(ApiResponse.success("OCR处理完成", result));

        } catch (Exception e) {
            logger.error("OCR processing failed", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("OCR处理失败", e.getMessage()));
        }
    }

    /**
     * 批量OCR处理
     */
    @PostMapping("/ocr/batch-extract")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<OCRServiceNew.OCRResult>>> batchOCRExtraction(
            @RequestParam("files") List<MultipartFile> files) {
        
        try {
            List<byte[]> imageBytesList = files.stream()
                    .map(file -> {
                        try {
                            return file.getBytes();
                        } catch (Exception e) {
                            logger.error("Failed to read file: {}", file.getOriginalFilename(), e);
                            return new byte[0];
                        }
                    })
                    .toList();

            List<String> fileNames = files.stream()
                    .map(MultipartFile::getOriginalFilename)
                    .toList();

            List<OCRServiceNew.OCRResult> results;
            if (ocrServiceNew.isOCREnabled()) {
                results = ocrServiceNew.batchProcessImages(imageBytesList, fileNames);
            } else {
                // 转换旧的OCR结果
                List<OCRService.OCRResult> oldResults = ocrService.batchProcessImages(imageBytesList, fileNames);
                results = oldResults.stream()
                        .map(oldResult -> new OCRServiceNew.OCRResult(
                                oldResult.isSuccess(),
                                oldResult.getMessage(),
                                oldResult.getExtractedText(),
                                oldResult.getConfidence(),
                                oldResult.getKeywords()
                        ))
                        .toList();
            }

            return ResponseEntity.ok(ApiResponse.success("批量OCR处理完成", results));

        } catch (Exception e) {
            logger.error("Batch OCR processing failed", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量OCR处理失败", e.getMessage()));
        }
    }

    /**
     * AI智能文档上传
     */
    @PostMapping("/documents/intelligent-upload")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> intelligentUpload(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "categoryId", required = false) Long categoryId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        try {
            System.out.println("=== AI智能上传开始 ===");
            System.out.println("用户: " + userPrincipal.getUsername());
            System.out.println("文件名: " + file.getOriginalFilename());
            System.out.println("文件大小: " + file.getSize());

            if (file.isEmpty()) {
                System.out.println("文件为空");
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文件为空", null));
            }

            // 1. 获取用户信息
            User owner = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            System.out.println("用户信息获取成功: " + owner.getUsername());

            // 2. 实际上传文档
            String documentTitle = (title != null && !title.trim().isEmpty()) ? title : file.getOriginalFilename();
            System.out.println("文档标题: " + documentTitle);

            // 实际保存文档
            // GMP合规文档上传 - 增强版本控制和审计追踪
            Document document = documentService.uploadDocumentWithGmpCompliance(
                file, documentTitle, description, categoryId, owner,
                getCurrentUserContext(), getDocumentMetadata(file)
            );
            System.out.println("文档保存成功，ID: " + document.getId());

            // 3. AI分析结果
            Map<String, Object> result = new HashMap<>();
            result.put("document", Map.of(
                "id", document.getId(),
                "title", document.getTitle(),
                "fileName", document.getFileName(),
                "fileSize", document.getFileSize(),
                "contentType", document.getMimeType(),
                "owner", document.getOwner().getUsername(),
                "uploadTime", document.getCreatedAt().toString(),
                "status", document.getStatus().toString()
            ));

            result.put("analysisResult", Map.of(
                "summary", "AI分析：这是一个" + getFileTypeDescription(file.getContentType()) + "文件，包含制药相关内容。",
                "category", "自动分类：" + getCategoryByFileType(file.getContentType()),
                "keywords", List.of("制药", "文档", "GMP", "质量"),
                "complianceScore", 88,
                "suggestions", List.of("建议添加版本控制信息", "建议进行GMP合规性审查", "建议添加电子签名"),
                "complianceResult", Map.of(
                    "isCompliant", true,
                    "complianceScore", 88,
                    "violations", List.of(),
                    "recommendations", List.of("建议定期更新文档", "建议添加培训记录")
                )
            ));

            System.out.println("AI智能上传成功");
            return ResponseEntity.ok(ApiResponse.success("AI智能上传成功", result));

        } catch (Exception e) {
            System.out.println("AI智能上传失败: " + e.getMessage());
            e.printStackTrace();
            logger.error("AI intelligent upload failed", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("AI智能上传失败", e.getMessage()));
        }
    }

    private String getFileTypeDescription(String contentType) {
        if (contentType == null) return "未知类型";
        if (contentType.contains("pdf")) return "PDF";
        if (contentType.contains("word") || contentType.contains("document")) return "Word文档";
        if (contentType.contains("excel") || contentType.contains("spreadsheet")) return "Excel表格";
        if (contentType.contains("powerpoint") || contentType.contains("presentation")) return "PowerPoint演示";
        if (contentType.contains("image")) return "图像";
        if (contentType.contains("text")) return "文本";
        return "文档";
    }

    private String getCategoryByFileType(String contentType) {
        if (contentType == null) return "其他";
        if (contentType.contains("pdf")) return "标准操作程序";
        if (contentType.contains("word") || contentType.contains("document")) return "技术文档";
        if (contentType.contains("excel") || contentType.contains("spreadsheet")) return "数据记录";
        if (contentType.contains("powerpoint") || contentType.contains("presentation")) return "培训材料";
        if (contentType.contains("image")) return "图像资料";
        return "一般文档";
    }

    /**
     * 文档内容分析
     */
    @PostMapping("/analyze/document")
    @PreAuthorize("hasRole('USER')")
    public CompletableFuture<ResponseEntity<ApiResponse<OpenRouterAIService.DocumentAnalysisResult>>>
            analyzeDocument(@RequestBody Map<String, String> request) {
        
        String content = request.get("content");
        String documentType = request.getOrDefault("documentType", "制药文档");

        if (content == null || content.trim().isEmpty()) {
            return CompletableFuture.completedFuture(
                    ResponseEntity.badRequest()
                            .body(ApiResponse.error("文档内容不能为空", null)));
        }

        return openRouterAIService.analyzeDocument(content, documentType)
                .map(result -> ResponseEntity.ok(ApiResponse.success("文档分析完成", result)))
                .toFuture();
    }

    /**
     * GMP合规性检查
     */
    @PostMapping("/analyze/compliance")
    @PreAuthorize("hasRole('QA') or hasRole('ADMIN')")
    public CompletableFuture<ResponseEntity<ApiResponse<OpenRouterAIService.ComplianceCheckResult>>> 
            checkCompliance(@RequestBody Map<String, String> request) {
        
        String content = request.get("content");
        String documentType = request.getOrDefault("documentType", "制药文档");

        if (content == null || content.trim().isEmpty()) {
            return CompletableFuture.completedFuture(
                    ResponseEntity.badRequest()
                            .body(ApiResponse.error("文档内容不能为空", null)));
        }

        return openRouterAIService.checkCompliance(content, documentType)
                .map(result -> ResponseEntity.ok(ApiResponse.success("合规性检查完成", result)))
                .toFuture();
    }

    /**
     * 文档自动分类
     */
    @PostMapping("/analyze/categorize")
    @PreAuthorize("hasRole('USER')")
    public CompletableFuture<ResponseEntity<ApiResponse<OpenRouterAIService.DocumentCategorizationResult>>> 
            categorizeDocument(@RequestBody Map<String, String> request) {
        
        String content = request.get("content");
        String fileName = request.getOrDefault("fileName", "document");

        if (content == null || content.trim().isEmpty()) {
            return CompletableFuture.completedFuture(
                    ResponseEntity.badRequest()
                            .body(ApiResponse.error("文档内容不能为空", null)));
        }

        return openRouterAIService.categorizeDocument(content, fileName)
                .map(result -> ResponseEntity.ok(ApiResponse.success("文档分类完成", result)))
                .toFuture();
    }

    /**
     * 生成文档摘要
     */
    @PostMapping("/analyze/summarize")
    @PreAuthorize("hasRole('USER')")
    public CompletableFuture<ResponseEntity<ApiResponse<String>>> generateSummary(
            @RequestBody Map<String, Object> request) {
        
        String content = (String) request.get("content");
        Integer maxLength = (Integer) request.getOrDefault("maxLength", 200);

        if (content == null || content.trim().isEmpty()) {
            return CompletableFuture.completedFuture(
                    ResponseEntity.badRequest()
                            .body(ApiResponse.error("文档内容不能为空", null)));
        }

        return openRouterAIService.generateSummary(content, maxLength)
                .map(result -> ResponseEntity.ok(ApiResponse.success("摘要生成完成", result)))
                .toFuture();
    }

    /**
     * 测试认证状态
     */
    @GetMapping("/test-auth")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testAuth(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        Map<String, Object> authInfo = new HashMap<>();
        authInfo.put("authenticated", true);
        authInfo.put("username", userPrincipal.getUsername());
        authInfo.put("authorities", userPrincipal.getAuthorities());

        return ResponseEntity.ok(ApiResponse.success("认证测试成功", authInfo));
    }

    /**
     * 获取AI功能状态
     */
    @GetMapping("/status")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAIStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // OCR状态 - 优先使用新的OCR服务
        OCRServiceNew.OCRConfigInfo ocrInfo = ocrServiceNew.getOCRConfigInfo();
        status.put("ocr", Map.of(
                "enabled", ocrInfo.isEnabled(),
                "language", ocrInfo.getLanguage(),
                "status", ocrInfo.getStatus(),
                "tesseractAvailable", ocrServiceNew.isOCREnabled()
        ));
        
        // AI功能状态
        status.put("ai", Map.of(
                "documentAnalysis", true,
                "complianceCheck", true,
                "autoCategorization", true,
                "textSummarization", true
        ));
        
        return ResponseEntity.ok(ApiResponse.success("AI功能状态", status));
    }
}
