@echo off
echo ========================================
echo 制药DMS系统 - 阶段1验证脚本
echo ========================================

echo.
echo 1. 编译项目...
call mvn clean compile -q
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译成功

echo.
echo 2. 运行测试...
call mvn test -q
if %errorlevel% neq 0 (
    echo ❌ 测试失败
    pause
    exit /b 1
)
echo ✅ 测试通过

echo.
echo 3. 系统验证完成！
echo.
echo 📋 系统信息:
echo   - 项目名称: 制药行业DMS系统
echo   - 版本: 1.0.0
echo   - 阶段: 第一阶段完成
echo   - 技术栈: Spring Boot 3.2 + PostgreSQL + JWT
echo.
echo 🚀 启动命令:
echo   开发模式: set SPRING_PROFILES_ACTIVE=h2 ^&^& mvn spring-boot:run
echo   生产模式: set SPRING_PROFILES_ACTIVE=postgresql ^&^& mvn spring-boot:run
echo   或使用脚本: start-h2.bat / start-postgresql.bat
echo.
echo 🌐 访问地址:
echo   Web界面: http://localhost:8081/dms/login
echo   网络访问: http://**************:8081/dms/login
echo.
echo 👤 默认账户:
echo   管理员: admin / admin123
echo   QA用户: qa_user / qa123
echo   普通用户: user / user123
echo.
echo ✅ 阶段1开发完成！
echo.
pause
