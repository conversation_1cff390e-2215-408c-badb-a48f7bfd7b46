<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>删除功能测试 - 制药DMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-trash-alt me-2 text-danger"></i>删除功能测试
                </h1>
                
                <!-- 用户删除测试 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-users me-2"></i>用户删除测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="userIdInput" class="form-label">用户ID</label>
                            <input type="number" class="form-control" id="userIdInput" placeholder="输入要删除的用户ID">
                        </div>
                        <button class="btn btn-danger" onclick="testDeleteUser()">
                            <i class="fas fa-trash me-2"></i>测试删除用户
                        </button>
                        <div id="userDeleteResult" class="mt-3"></div>
                    </div>
                </div>

                <!-- 文档删除测试 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-file-alt me-2"></i>文档删除测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="documentIdInput" class="form-label">文档ID</label>
                            <input type="number" class="form-control" id="documentIdInput" placeholder="输入要删除的文档ID">
                        </div>
                        <button class="btn btn-danger" onclick="testDeleteDocument()">
                            <i class="fas fa-trash me-2"></i>测试删除文档
                        </button>
                        <div id="documentDeleteResult" class="mt-3"></div>
                    </div>
                </div>

                <!-- 获取用户列表 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-list me-2"></i>用户列表</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="loadUsers()">
                            <i class="fas fa-sync me-2"></i>加载用户列表
                        </button>
                        <div id="usersList" class="mt-3"></div>
                    </div>
                </div>

                <!-- 获取文档列表 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-list me-2"></i>文档列表</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="loadDocuments()">
                            <i class="fas fa-sync me-2"></i>加载文档列表
                        </button>
                        <div id="documentsList" class="mt-3"></div>
                    </div>
                </div>

                <!-- 返回按钮 -->
                <div class="text-center">
                    <a href="/dms/dashboard" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回仪表板
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/dms/js/auth.js"></script>
    <script>
        // 测试删除用户
        async function testDeleteUser() {
            const userId = document.getElementById('userIdInput').value;
            if (!userId) {
                alert('请输入用户ID');
                return;
            }

            const resultDiv = document.getElementById('userDeleteResult');
            resultDiv.innerHTML = '<div class="spinner-border text-primary" role="status"></div> 正在删除用户...';

            try {
                console.log('=== 测试删除用户 ===');
                console.log('用户ID:', userId);

                const response = await authUtils.secureApiCall(`/dms/api/users/${userId}`, {
                    method: 'DELETE'
                });

                console.log('删除用户响应状态:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('删除用户成功:', result);
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            用户删除成功！${result.message}
                        </div>
                    `;
                } else {
                    const result = await response.json();
                    console.error('删除用户失败:', result);
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            删除用户失败：${result.message || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('删除用户异常:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        删除用户异常：${error.message}
                    </div>
                `;
            }
        }

        // 测试删除文档
        async function testDeleteDocument() {
            const documentId = document.getElementById('documentIdInput').value;
            if (!documentId) {
                alert('请输入文档ID');
                return;
            }

            const resultDiv = document.getElementById('documentDeleteResult');
            resultDiv.innerHTML = '<div class="spinner-border text-primary" role="status"></div> 正在删除文档...';

            try {
                console.log('=== 测试删除文档 ===');
                console.log('文档ID:', documentId);

                const response = await authUtils.secureApiCall(`/dms/api/documents/${documentId}`, {
                    method: 'DELETE'
                });

                console.log('删除文档响应状态:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('删除文档成功:', result);
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            文档删除成功！${result.message}
                        </div>
                    `;
                } else {
                    const result = await response.json();
                    console.error('删除文档失败:', result);
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            删除文档失败：${result.message || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('删除文档异常:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        删除文档异常：${error.message}
                    </div>
                `;
            }
        }

        // 加载用户列表
        async function loadUsers() {
            const resultDiv = document.getElementById('usersList');
            resultDiv.innerHTML = '<div class="spinner-border text-primary" role="status"></div> 正在加载用户列表...';

            try {
                const response = await authUtils.secureApiCall('/dms/api/users?page=0&size=10');
                
                if (response.ok) {
                    const result = await response.json();
                    const users = result.data.content;
                    
                    let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr><th>ID</th><th>用户名</th><th>邮箱</th><th>状态</th><th>操作</th></tr></thead><tbody>';
                    
                    users.forEach(user => {
                        html += `
                            <tr>
                                <td>${user.id}</td>
                                <td>${user.username}</td>
                                <td>${user.email}</td>
                                <td>${user.isActive ? '<span class="badge bg-success">活跃</span>' : '<span class="badge bg-secondary">非活跃</span>'}</td>
                                <td>
                                    <button class="btn btn-sm btn-danger" onclick="document.getElementById('userIdInput').value='${user.id}'; testDeleteUser();">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table></div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-danger">加载用户列表失败</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="alert alert-danger">加载用户列表异常：' + error.message + '</div>';
            }
        }

        // 加载文档列表
        async function loadDocuments() {
            const resultDiv = document.getElementById('documentsList');
            resultDiv.innerHTML = '<div class="spinner-border text-primary" role="status"></div> 正在加载文档列表...';

            try {
                const response = await authUtils.secureApiCall('/dms/api/documents?page=0&size=10');
                
                if (response.ok) {
                    const result = await response.json();
                    const documents = result.data.content;
                    
                    let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr><th>ID</th><th>标题</th><th>状态</th><th>创建时间</th><th>操作</th></tr></thead><tbody>';
                    
                    documents.forEach(doc => {
                        html += `
                            <tr>
                                <td>${doc.id}</td>
                                <td>${doc.title}</td>
                                <td><span class="badge bg-info">${doc.status}</span></td>
                                <td>${new Date(doc.createdAt).toLocaleString()}</td>
                                <td>
                                    <button class="btn btn-sm btn-danger" onclick="document.getElementById('documentIdInput').value='${doc.id}'; testDeleteDocument();">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table></div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-danger">加载文档列表失败</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="alert alert-danger">加载文档列表异常：' + error.message + '</div>';
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('删除功能测试页面已加载');
        });
    </script>
</body>
</html>
