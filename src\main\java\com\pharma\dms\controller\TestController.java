package com.pharma.dms.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.repository.DocumentRepository;
import com.pharma.dms.repository.UserRepository;
import com.pharma.dms.security.UserPrincipal;
import com.pharma.dms.service.DocumentService;

@RestController
@RequestMapping("/api/test")
public class TestController {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private DocumentService documentService;

    @GetMapping("/basic")
    public ResponseEntity<ApiResponse<String>> basicTest() {
        try {
            System.out.println("=== 基本测试开始 ===");
            return ResponseEntity.ok(ApiResponse.success("Basic test successful", "System is working"));
        } catch (Exception e) {
            System.out.println("基本测试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Basic test failed", e.getMessage()));
        }
    }

    @GetMapping("/database")
    public ResponseEntity<ApiResponse<Map<String, Object>>> databaseTest() {
        try {
            System.out.println("=== 数据库测试开始 ===");
            
            Map<String, Object> result = new HashMap<>();
            
            // 测试用户表
            long userCount = userRepository.count();
            result.put("userCount", userCount);
            System.out.println("用户数量: " + userCount);
            
            // 测试文档表
            long documentCount = documentRepository.count();
            result.put("documentCount", documentCount);
            System.out.println("文档数量: " + documentCount);
            
            System.out.println("数据库测试成功");
            return ResponseEntity.ok(ApiResponse.success("Database test successful", result));
        } catch (Exception e) {
            System.out.println("数据库测试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Database test failed", e.getMessage()));
        }
    }

    @GetMapping("/users")
    public ResponseEntity<ApiResponse<Map<String, Object>>> usersTest() {
        try {
            System.out.println("=== 用户测试开始 ===");
            
            Map<String, Object> result = new HashMap<>();
            
            // 获取所有用户
            var users = userRepository.findAll();
            result.put("totalUsers", users.size());
            
            // 获取用户名列表
            var usernames = users.stream().map(u -> u.getUsername()).toList();
            result.put("usernames", usernames);
            
            System.out.println("用户测试成功，用户数: " + users.size());
            return ResponseEntity.ok(ApiResponse.success("Users test successful", result));
        } catch (Exception e) {
            System.out.println("用户测试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Users test failed", e.getMessage()));
        }
    }

    @GetMapping("/documents")
    public ResponseEntity<ApiResponse<Map<String, Object>>> documentsTest() {
        try {
            System.out.println("=== 文档服务测试开始 ===");

            Map<String, Object> result = new HashMap<>();

            // 测试文档总数
            System.out.println("测试文档总数...");
            long totalCount = documentService.getTotalDocumentCount();
            result.put("totalDocuments", totalCount);
            System.out.println("文档总数: " + totalCount);

            // 测试获取所有文档（分页）
            System.out.println("测试获取文档列表...");
            var pageable = org.springframework.data.domain.PageRequest.of(0, 5);
            var documents = documentService.getAllDocuments(pageable);
            result.put("documentsPage", Map.of(
                "totalElements", documents.getTotalElements(),
                "totalPages", documents.getTotalPages(),
                "size", documents.getSize(),
                "numberOfElements", documents.getNumberOfElements()
            ));
            System.out.println("文档分页查询成功，总元素: " + documents.getTotalElements());

            System.out.println("文档服务测试成功");
            return ResponseEntity.ok(ApiResponse.success("Documents test successful", result));
        } catch (Exception e) {
            System.out.println("文档服务测试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Documents test failed", e.getMessage()));
        }
    }

    @GetMapping("/document-categories")
    public ResponseEntity<ApiResponse<Map<String, Object>>> documentCategoriesTest() {
        try {
            System.out.println("=== 文档分类测试开始 ===");

            Map<String, Object> result = new HashMap<>();

            // 直接查询文档分类表
            var categories = documentRepository.findAll();
            result.put("directQuery", "Success - " + categories.size() + " documents found");

            System.out.println("文档分类测试成功");
            return ResponseEntity.ok(ApiResponse.success("Document categories test successful", result));
        } catch (Exception e) {
            System.out.println("文档分类测试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Document categories test failed", e.getMessage()));
        }
    }

    @GetMapping("/auth-test")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> authTest(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 认证测试开始 ===");

            Map<String, Object> result = new HashMap<>();

            if (userPrincipal != null) {
                System.out.println("用户主体存在: " + userPrincipal.getUsername());
                result.put("username", userPrincipal.getUsername());
                result.put("authorities", userPrincipal.getAuthorities().toString());
                result.put("id", userPrincipal.getId());
            } else {
                System.out.println("用户主体为null");
                result.put("error", "UserPrincipal is null");
            }

            System.out.println("认证测试成功");
            return ResponseEntity.ok(ApiResponse.success("Auth test successful", result));
        } catch (Exception e) {
            System.out.println("认证测试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Auth test failed", e.getMessage()));
        }
    }

    @GetMapping("/documents-with-auth")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> documentsWithAuthTest(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 带认证的文档测试开始 ===");

            Map<String, Object> result = new HashMap<>();

            if (userPrincipal != null) {
                System.out.println("用户: " + userPrincipal.getUsername());

                // 测试分页查询
                var pageable = org.springframework.data.domain.PageRequest.of(0, 5);
                var documents = documentService.getAllDocuments(pageable);

                result.put("user", userPrincipal.getUsername());
                result.put("totalDocuments", documents.getTotalElements());
                result.put("documentsInPage", documents.getNumberOfElements());

                System.out.println("带认证的文档测试成功");
            } else {
                result.put("error", "No authenticated user");
            }

            return ResponseEntity.ok(ApiResponse.success("Documents with auth test successful", result));
        } catch (Exception e) {
            System.out.println("带认证的文档测试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Documents with auth test failed", e.getMessage()));
        }
    }

    @GetMapping("/documents-simple")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> documentsSimpleTest(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 简化文档API测试开始 ===");

            Map<String, Object> result = new HashMap<>();

            if (userPrincipal != null) {
                System.out.println("用户: " + userPrincipal.getUsername());

                // 直接查询文档表，不使用分页
                var documents = documentRepository.findAll();
                System.out.println("找到文档数量: " + documents.size());

                // 创建简化的文档信息
                var simpleDocs = documents.stream().map(doc -> {
                    Map<String, Object> simpleDoc = new HashMap<>();
                    simpleDoc.put("id", doc.getId());
                    simpleDoc.put("title", doc.getTitle());
                    simpleDoc.put("fileName", doc.getFileName());
                    simpleDoc.put("fileSize", doc.getFileSize());
                    simpleDoc.put("status", doc.getStatus().toString());
                    simpleDoc.put("createdAt", doc.getCreatedAt().toString());
                    return simpleDoc;
                }).toList();

                result.put("user", userPrincipal.getUsername());
                result.put("totalDocuments", documents.size());
                result.put("documents", simpleDocs);

                System.out.println("简化文档API测试成功");
            } else {
                result.put("error", "No authenticated user");
            }

            return ResponseEntity.ok(ApiResponse.success("Simple documents test successful", result));
        } catch (Exception e) {
            System.out.println("简化文档API测试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Simple documents test failed", e.getMessage()));
        }
    }

    @PostMapping("/upload-test")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> uploadTest(
            @RequestParam("file") org.springframework.web.multipart.MultipartFile file,
            @RequestParam("title") String title,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 上传测试开始 ===");
            System.out.println("文件名: " + file.getOriginalFilename());
            System.out.println("文件大小: " + file.getSize());
            System.out.println("标题: " + title);
            System.out.println("用户: " + userPrincipal.getUsername());

            Map<String, Object> result = new HashMap<>();
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());
            result.put("title", title);
            result.put("user", userPrincipal.getUsername());
            result.put("contentType", file.getContentType());
            result.put("isEmpty", file.isEmpty());

            System.out.println("上传测试成功");
            return ResponseEntity.ok(ApiResponse.success("Upload test successful", result));
        } catch (Exception e) {
            System.out.println("上传测试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Upload test failed", e.getMessage()));
        }
    }

    @PostMapping("/upload-debug")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> uploadDebug(
            @RequestParam("file") org.springframework.web.multipart.MultipartFile file,
            @RequestParam("title") String title,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 上传调试开始 ===");

            Map<String, Object> result = new HashMap<>();

            // 步骤1：检查用户
            System.out.println("步骤1：获取用户信息");
            var user = userRepository.findByUsername(userPrincipal.getUsername()).orElse(null);
            if (user == null) {
                result.put("error", "User not found");
                return ResponseEntity.status(500).body(ApiResponse.error("User not found", "User not found"));
            }
            result.put("step1_user", "OK - " + user.getUsername());
            System.out.println("用户找到: " + user.getUsername());

            // 步骤2：检查文件
            System.out.println("步骤2：检查文件");
            if (file.isEmpty()) {
                result.put("error", "File is empty");
                return ResponseEntity.status(500).body(ApiResponse.error("File is empty", "File is empty"));
            }
            result.put("step2_file", "OK - " + file.getOriginalFilename() + " (" + file.getSize() + " bytes)");
            System.out.println("文件检查通过");

            // 步骤3：检查上传目录配置
            System.out.println("步骤3：检查上传目录");
            String uploadDir = "uploads"; // 默认值
            result.put("step3_uploadDir", "OK - " + uploadDir);
            System.out.println("上传目录: " + uploadDir);

            // 步骤4：尝试创建目录
            System.out.println("步骤4：创建上传目录");
            try {
                java.nio.file.Path uploadPath = java.nio.file.Paths.get(uploadDir);
                if (!java.nio.file.Files.exists(uploadPath)) {
                    java.nio.file.Files.createDirectories(uploadPath);
                }
                result.put("step4_directory", "OK - " + uploadPath.toAbsolutePath());
                System.out.println("目录创建成功: " + uploadPath.toAbsolutePath());
            } catch (Exception e) {
                result.put("step4_directory", "FAILED - " + e.getMessage());
                System.out.println("目录创建失败: " + e.getMessage());
                return ResponseEntity.status(500).body(ApiResponse.error("Directory creation failed", e.getMessage()));
            }

            result.put("status", "All steps passed");
            System.out.println("所有步骤通过");

            return ResponseEntity.ok(ApiResponse.success("Upload debug successful", result));
        } catch (Exception e) {
            System.out.println("上传调试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Upload debug failed", e.getMessage()));
        }
    }

    @PostMapping("/ocr-debug")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> ocrDebug(
            @RequestParam("files") org.springframework.web.multipart.MultipartFile[] files,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== OCR调试开始 ===");

            Map<String, Object> result = new HashMap<>();

            // 步骤1：检查文件
            System.out.println("步骤1：检查上传的文件");
            result.put("fileCount", files.length);

            List<Map<String, Object>> fileInfos = new ArrayList<>();
            for (int i = 0; i < files.length; i++) {
                var file = files[i];
                Map<String, Object> fileInfo = new HashMap<>();
                fileInfo.put("index", i);
                fileInfo.put("originalName", file.getOriginalFilename());
                fileInfo.put("size", file.getSize());
                fileInfo.put("contentType", file.getContentType());
                fileInfo.put("isEmpty", file.isEmpty());

                // 检查是否是图像文件
                boolean isImage = file.getContentType() != null &&
                                file.getContentType().startsWith("image/");
                fileInfo.put("isImage", isImage);

                // 尝试读取图像
                try {
                    java.awt.image.BufferedImage image = javax.imageio.ImageIO.read(
                        new java.io.ByteArrayInputStream(file.getBytes()));
                    if (image != null) {
                        fileInfo.put("imageWidth", image.getWidth());
                        fileInfo.put("imageHeight", image.getHeight());
                        fileInfo.put("imageReadable", true);
                    } else {
                        fileInfo.put("imageReadable", false);
                    }
                } catch (Exception e) {
                    fileInfo.put("imageReadable", false);
                    fileInfo.put("imageError", e.getMessage());
                }

                fileInfos.add(fileInfo);
                System.out.println("文件 " + i + ": " + file.getOriginalFilename() +
                                 " (" + file.getSize() + " bytes, " + file.getContentType() + ")");
            }

            result.put("files", fileInfos);
            result.put("user", userPrincipal.getUsername());
            result.put("status", "OCR debug completed");

            System.out.println("OCR调试完成");
            return ResponseEntity.ok(ApiResponse.success("OCR debug successful", result));
        } catch (Exception e) {
            System.out.println("OCR调试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("OCR debug failed", e.getMessage()));
        }
    }

    @GetMapping("/user-me-debug")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> userMeDebug(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 用户信息调试开始 ===");

            Map<String, Object> result = new HashMap<>();

            // 步骤1：检查UserPrincipal
            if (userPrincipal == null) {
                System.out.println("UserPrincipal为null");
                result.put("error", "UserPrincipal is null");
                return ResponseEntity.status(400).body(ApiResponse.error("UserPrincipal is null", "UserPrincipal is null"));
            }

            result.put("step1_userPrincipal", "OK - " + userPrincipal.getUsername());
            System.out.println("UserPrincipal存在: " + userPrincipal.getUsername());

            // 步骤2：检查用户查找
            try {
                var user = userRepository.findByUsername(userPrincipal.getUsername());
                if (user.isPresent()) {
                    result.put("step2_userFound", "OK - " + user.get().getUsername());
                    result.put("userId", user.get().getId());
                    result.put("userEmail", user.get().getEmail());
                    result.put("userFirstName", user.get().getFirstName());
                    result.put("userLastName", user.get().getLastName());
                    System.out.println("用户找到: " + user.get().getUsername());
                } else {
                    result.put("step2_userFound", "FAILED - User not found in database");
                    System.out.println("用户未找到: " + userPrincipal.getUsername());
                }
            } catch (Exception e) {
                result.put("step2_userFound", "ERROR - " + e.getMessage());
                System.out.println("用户查找异常: " + e.getMessage());
            }

            result.put("status", "User debug completed");
            System.out.println("用户信息调试完成");

            return ResponseEntity.ok(ApiResponse.success("User debug successful", result));
        } catch (Exception e) {
            System.out.println("用户信息调试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("User debug failed", e.getMessage()));
        }
    }

    @GetMapping("/download-debug/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> downloadDebug(
            @PathVariable Long id,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 下载调试开始 ===");
            System.out.println("文档ID: " + id);
            System.out.println("用户: " + userPrincipal.getUsername());

            Map<String, Object> result = new HashMap<>();

            // 步骤1：检查文档是否存在
            var document = documentRepository.findById(id);
            if (document.isPresent()) {
                result.put("step1_documentFound", "OK - " + document.get().getTitle());
                result.put("documentPath", document.get().getFilePath());
                result.put("documentSize", document.get().getFileSize());
                result.put("documentMimeType", document.get().getMimeType());

                // 步骤2：检查文件是否存在
                try {
                    java.nio.file.Path filePath = java.nio.file.Paths.get(document.get().getFilePath());
                    boolean fileExists = java.nio.file.Files.exists(filePath);
                    result.put("step2_fileExists", fileExists ? "OK" : "FAILED - File not found");
                    if (fileExists) {
                        result.put("actualFileSize", java.nio.file.Files.size(filePath));
                    }
                } catch (Exception e) {
                    result.put("step2_fileExists", "ERROR - " + e.getMessage());
                }

                // 步骤3：检查用户权限
                try {
                    var user = userRepository.findByUsername(userPrincipal.getUsername());
                    if (user.isPresent()) {
                        result.put("step3_userFound", "OK - " + user.get().getUsername());
                        result.put("userRoles", user.get().getRoles().stream()
                                .map(role -> role.getName().name()).toList());
                    } else {
                        result.put("step3_userFound", "FAILED - User not found");
                    }
                } catch (Exception e) {
                    result.put("step3_userFound", "ERROR - " + e.getMessage());
                }

            } else {
                result.put("step1_documentFound", "FAILED - Document not found");
            }

            result.put("status", "Download debug completed");
            System.out.println("下载调试完成");

            return ResponseEntity.ok(ApiResponse.success("Download debug successful", result));
        } catch (Exception e) {
            System.out.println("下载调试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Download debug failed", e.getMessage()));
        }
    }

    @GetMapping("/simple-download/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<String> simpleDownloadTest(
            @PathVariable Long id,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 简单下载测试 ===");
            System.out.println("文档ID: " + id);
            System.out.println("用户: " + userPrincipal.getUsername());

            return ResponseEntity.ok("下载测试成功 - 文档ID: " + id + ", 用户: " + userPrincipal.getUsername());
        } catch (Exception e) {
            System.out.println("简单下载测试失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body("下载测试失败: " + e.getMessage());
        }
    }

    @GetMapping("/certificate-download/{certificateNumber}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<byte[]> downloadCertificate(
            @PathVariable String certificateNumber,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 证书下载测试 ===");
            System.out.println("证书编号: " + certificateNumber);
            System.out.println("用户: " + userPrincipal.getUsername());

            // 创建一个简单的PDF内容（使用基本的PDF格式）
            String pdfContent = "%PDF-1.4\n" +
                    "1 0 obj\n" +
                    "<<\n" +
                    "/Type /Catalog\n" +
                    "/Pages 2 0 R\n" +
                    ">>\n" +
                    "endobj\n" +
                    "2 0 obj\n" +
                    "<<\n" +
                    "/Type /Pages\n" +
                    "/Kids [3 0 R]\n" +
                    "/Count 1\n" +
                    ">>\n" +
                    "endobj\n" +
                    "3 0 obj\n" +
                    "<<\n" +
                    "/Type /Page\n" +
                    "/Parent 2 0 R\n" +
                    "/MediaBox [0 0 612 792]\n" +
                    "/Contents 4 0 R\n" +
                    "/Resources <<\n" +
                    "/Font <<\n" +
                    "/F1 5 0 R\n" +
                    ">>\n" +
                    ">>\n" +
                    ">>\n" +
                    "endobj\n" +
                    "4 0 obj\n" +
                    "<<\n" +
                    "/Length 200\n" +
                    ">>\n" +
                    "stream\n" +
                    "BT\n" +
                    "/F1 24 Tf\n" +
                    "100 700 Td\n" +
                    "(Training Certificate) Tj\n" +
                    "0 -50 Td\n" +
                    "/F1 12 Tf\n" +
                    "(Certificate Number: " + certificateNumber + ") Tj\n" +
                    "0 -30 Td\n" +
                    "(User: " + userPrincipal.getUsername() + ") Tj\n" +
                    "0 -30 Td\n" +
                    "(Download Time: " + java.time.LocalDateTime.now() + ") Tj\n" +
                    "ET\n" +
                    "endstream\n" +
                    "endobj\n" +
                    "5 0 obj\n" +
                    "<<\n" +
                    "/Type /Font\n" +
                    "/Subtype /Type1\n" +
                    "/BaseFont /Helvetica\n" +
                    ">>\n" +
                    "endobj\n" +
                    "xref\n" +
                    "0 6\n" +
                    "0000000000 65535 f \n" +
                    "0000000009 00000 n \n" +
                    "0000000074 00000 n \n" +
                    "0000000120 00000 n \n" +
                    "0000000285 00000 n \n" +
                    "0000000595 00000 n \n" +
                    "trailer\n" +
                    "<<\n" +
                    "/Size 6\n" +
                    "/Root 1 0 R\n" +
                    ">>\n" +
                    "startxref\n" +
                    "673\n" +
                    "%%EOF";

            byte[] pdfBytes = pdfContent.getBytes("UTF-8");

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(org.springframework.http.MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", "certificate-" + certificateNumber + ".pdf");
            headers.setContentLength(pdfBytes.length);

            System.out.println("证书下载成功，文件大小: " + pdfBytes.length + " bytes");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);

        } catch (Exception e) {
            System.out.println("证书下载失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(null);
        }
    }
}
