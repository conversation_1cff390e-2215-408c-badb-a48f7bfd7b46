<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Pharmaceutical DMS</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-container {
            max-width: 400px;
            margin: 0 auto;
        }
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .login-header h2 {
            color: #333;
            font-weight: 600;
        }
        .login-header .subtitle {
            color: #666;
            font-size: 0.9rem;
        }
        .form-control {
            border-radius: 10px;
            border: 1px solid #ddd;
            padding: 0.75rem 1rem;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem;
            font-weight: 600;
            width: 100%;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .demo-credentials {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <i class="fas fa-pills fa-3x text-primary mb-3"></i>
                    <h2>制药文档管理系统</h2>
                    <p class="subtitle">Pharmaceutical Document Management System</p>
                </div>

                <div id="alert-container"></div>

                <form id="loginForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="username" name="username" placeholder="请输入用户名" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" placeholder="请输入密码" required>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        登录
                    </button>
                </form>

                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        安全登录 · GMP合规 · 数据保护
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;

            // 清除之前的错误信息
            document.getElementById('alert-container').innerHTML = '';

            // 输入验证
            if (!username) {
                showAlert('请输入用户名', 'danger');
                document.getElementById('username').focus();
                return;
            }

            if (!password) {
                showAlert('请输入密码', 'danger');
                document.getElementById('password').focus();
                return;
            }

            if (username.length < 3) {
                showAlert('用户名至少需要3个字符', 'danger');
                document.getElementById('username').focus();
                return;
            }

            if (password.length < 6) {
                showAlert('密码至少需要6个字符', 'danger');
                document.getElementById('password').focus();
                return;
            }

            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>登录中...';

            try {
                console.log('=== 登录请求开始 ===');
                console.log('用户名:', username);

                const response = await fetch('/dms/api/auth/signin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                console.log('登录响应状态:', response.status);

                const data = await response.json();
                console.log('登录响应数据:', data);

                if (response.ok && data.token) {
                    console.log('✅ 登录成功，保存认证信息');

                    // Store token - try both token and accessToken for compatibility
                    const token = data.token || data.accessToken;
                    const userData = {
                        id: data.id,
                        username: data.username,
                        email: data.email,
                        firstName: data.firstName,
                        lastName: data.lastName,
                        roles: data.roles
                    };

                    localStorage.setItem('token', token);
                    localStorage.setItem('user', JSON.stringify(userData));

                    console.log('💾 Token已保存，长度:', token.length);
                    console.log('👤 用户信息已保存:', userData.username);

                    // 显示成功消息
                    showAlert('登录成功，正在跳转...', 'success');

                    // 延迟跳转
                    setTimeout(() => {
                        window.location.href = '/dms/dashboard';
                    }, 1000);

                } else {
                    console.log('登录失败:', data.message);

                    let errorMessage = '登录失败';
                    if (data.message) {
                        if (data.message.includes('Invalid credentials') ||
                            data.message.includes('用户名或密码错误')) {
                            errorMessage = '用户名或密码错误';
                        } else if (data.message.includes('Account locked') ||
                                  data.message.includes('账户被锁定')) {
                            errorMessage = '账户被锁定，请联系管理员';
                        } else if (data.message.includes('Account disabled') ||
                                  data.message.includes('账户被禁用')) {
                            errorMessage = '账户被禁用，请联系管理员';
                        } else {
                            errorMessage = data.message;
                        }
                    }

                    showAlert(errorMessage, 'danger');

                    // 清空密码字段
                    document.getElementById('password').value = '';
                    document.getElementById('password').focus();
                }
            } catch (error) {
                console.error('登录异常:', error);

                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    showAlert('无法连接到服务器，请检查网络连接', 'danger');
                } else if (error.name === 'SyntaxError') {
                    showAlert('服务器响应格式错误，请稍后重试', 'danger');
                } else {
                    showAlert('网络错误，请稍后重试', 'danger');
                }

                // 清空密码字段
                document.getElementById('password').value = '';
            } finally {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText;
            }
        });
        
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            alertContainer.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        // Check if user is already logged in and redirect
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('token');
            if (token) {
                // Validate token before redirecting
                fetch('/dms/api/auth/validate', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                }).then(response => {
                    if (response.ok) {
                        window.location.href = '/dms/dashboard';
                    } else {
                        // Only clear invalid tokens
                        localStorage.removeItem('token');
                        localStorage.removeItem('user');
                    }
                }).catch(() => {
                    // Clear tokens on network error
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                });
            }
        });
    </script>
</body>
</html>
