<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle} + ' - Pharmaceutical DMS'">Pharmaceutical DMS</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        .navbar-brand {
            font-weight: bold;
            color: #0d6efd !important;
        }
        .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
            border-radius: 0.375rem;
        }
        .nav-link.active {
            background-color: #0d6efd;
            color: white !important;
            border-radius: 0.375rem;
        }
        .main-content {
            padding: 2rem;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        .footer {
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            padding: 1rem 0;
            margin-top: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="navbar-brand mb-4">
                        <i class="fas fa-pills me-2"></i>
                        Pharma DMS
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/dashboard')} ? 'active'" 
                               th:href="@{/dashboard}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/users')} ? 'active'" 
                               th:href="@{/users}">
                                <i class="fas fa-users me-2"></i>
                                User Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/documents')} ? 'active'"
                               th:href="@{/documents}">
                                <i class="fas fa-file-alt me-2"></i>
                                Documents
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/system-overview')} ? 'active'"
                               th:href="@{/system-overview}">
                                <i class="fas fa-chart-line me-2"></i>
                                System Overview
                            </a>
                        </li>

                        <!-- Training Section -->
                        <hr class="sidebar-divider">
                        <div class="sidebar-heading">
                            Training
                        </div>

                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/my-training')} ? 'active'"
                               th:href="@{/my-training}">
                                <i class="fas fa-user-graduate me-2"></i>
                                My Training
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/training-courses')} ? 'active'"
                               th:href="@{/training-courses}">
                                <i class="fas fa-graduation-cap me-2"></i>
                                Training Courses
                            </a>
                        </li>

                        <li class="nav-item" sec:authorize="hasRole('ADMIN') or hasRole('QA')">
                            <a class="nav-link" th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/training-management')} ? 'active'"
                               th:href="@{/training-management}">
                                <i class="fas fa-chalkboard-teacher me-2"></i>
                                Training Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-chart-bar me-2"></i>
                                Reports
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </a>
                        </li>
                    </ul>
                    
                    <hr>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/profile}">
                                <i class="fas fa-user me-2"></i>
                                Profile
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2" th:text="${pageTitle}">Page Title</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-bell"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Page content -->
                <div th:fragment="content">
                    <!-- Content will be replaced by specific pages -->
                </div>
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer mt-auto py-3">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <span class="text-muted">&copy; 2024 Pharmaceutical DMS. All rights reserved.</span>
                </div>
                <div class="col-md-6 text-end">
                    <span class="text-muted">Version 1.0.0</span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                localStorage.removeItem('token');
                window.location.href = '/dms/login';
            }
        }
    </script>
</body>
</html>
