#!/bin/bash

# 制药DMS快速启动脚本
# 最简化的启动命令

echo "🚀 快速启动制药DMS系统..."

# 切换到应用目录
cd /d/learn/java/dms

# 启动PostgreSQL（如果未运行）
if ! pgrep -f postgres > /dev/null; then
    echo "🗄️  启动数据库..."
    /d/sql/pgsql/bin/pg_ctl -D /d/sql/pgsql/data start > /dev/null 2>&1
    sleep 3
fi

# 启动应用程序（如果未运行）
if ! pgrep -f "dms-1.0.0.jar" > /dev/null; then
    echo "🚀 启动应用程序..."
    nohup java -jar target/dms-1.0.0.jar \
        --spring.profiles.active=postgresql \
        --server.address=0.0.0.0 \
        > application.log 2>&1 &
    
    echo "⏳ 等待启动完成..."
    sleep 10
fi

echo "✅ 系统启动完成!"
echo "🌐 访问地址: http://**************:8081/dms/login"
