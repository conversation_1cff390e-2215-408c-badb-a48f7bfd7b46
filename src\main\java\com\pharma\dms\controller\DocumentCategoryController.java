package com.pharma.dms.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.DocumentCategory;
import com.pharma.dms.service.DocumentCategoryService;

@RestController
@RequestMapping("/api/document-categories")
@CrossOrigin(origins = "*", maxAge = 3600)
public class DocumentCategoryController {

    @Autowired
    private DocumentCategoryService categoryService;

    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<DocumentCategory>>> getAllCategories() {
        List<DocumentCategory> categories = categoryService.getAllCategories();
        return ResponseEntity.ok(ApiResponse.success("Categories retrieved successfully", categories));
    }

    @GetMapping("/active")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getActiveCategories() {
        try {
            System.out.println("=== 获取活跃文档分类 ===");

            List<DocumentCategory> categories = categoryService.getActiveCategories();
            System.out.println("找到分类数量: " + categories.size());

            // 创建简化的分类信息，避免序列化问题
            List<Map<String, Object>> simplifiedCategories = categories.stream()
                    .map(category -> {
                        Map<String, Object> categoryInfo = new HashMap<>();
                        categoryInfo.put("id", category.getId());
                        categoryInfo.put("name", category.getName());
                        categoryInfo.put("code", category.getCode());
                        categoryInfo.put("description", category.getDescription());
                        categoryInfo.put("isActive", category.getIsActive());
                        categoryInfo.put("sortOrder", category.getSortOrder());

                        // 安全地添加父分类信息
                        try {
                            if (category.getParentCategory() != null) {
                                categoryInfo.put("parentCategoryName", category.getParentCategory().getName());
                                categoryInfo.put("parentCategoryId", category.getParentCategory().getId());
                            }
                        } catch (Exception e) {
                            categoryInfo.put("parentCategoryName", null);
                            categoryInfo.put("parentCategoryId", null);
                        }

                        return categoryInfo;
                    })
                    .toList();

            System.out.println("活跃文档分类获取成功");
            return ResponseEntity.ok(ApiResponse.success("Active categories retrieved", simplifiedCategories));
        } catch (Exception e) {
            System.out.println("获取活跃文档分类失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to get active categories", e.getMessage()));
        }
    }

    @GetMapping("/root")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<DocumentCategory>>> getRootCategories() {
        List<DocumentCategory> categories = categoryService.getRootCategories();
        return ResponseEntity.ok(ApiResponse.success("Root categories retrieved", categories));
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<DocumentCategory>> getCategoryById(@PathVariable Long id) {
        Optional<DocumentCategory> category = categoryService.getCategoryById(id);
        
        if (category.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success("Category found", category.get()));
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/{id}/subcategories")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<DocumentCategory>>> getSubCategories(@PathVariable Long id) {
        List<DocumentCategory> subCategories = categoryService.getSubCategories(id);
        return ResponseEntity.ok(ApiResponse.success("Subcategories retrieved", subCategories));
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<DocumentCategory>> createCategory(@RequestBody DocumentCategory category) {
        try {
            DocumentCategory savedCategory = categoryService.createCategory(category);
            return ResponseEntity.ok(ApiResponse.success("Category created successfully", savedCategory));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to create category", e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<DocumentCategory>> updateCategory(@PathVariable Long id, 
                                                                       @RequestBody DocumentCategory categoryDetails) {
        try {
            DocumentCategory updatedCategory = categoryService.updateCategory(id, categoryDetails);
            return ResponseEntity.ok(ApiResponse.success("Category updated successfully", updatedCategory));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update category", e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteCategory(@PathVariable Long id) {
        try {
            categoryService.deleteCategory(id);
            return ResponseEntity.ok(ApiResponse.success("Category deleted successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to delete category", e.getMessage()));
        }
    }

    @GetMapping("/{id}/document-count")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Long>> getDocumentCount(@PathVariable Long id) {
        long count = categoryService.getDocumentCount(id);
        return ResponseEntity.ok(ApiResponse.success("Document count retrieved", count));
    }
}
