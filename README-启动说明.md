# 制药DMS系统启动说明

## 🚀 快速启动（推荐）

电脑重启后，打开Git Bash，执行以下命令：

```bash
cd /d/learn/java/dms
bash start-dms.sh
```

## 📋 启动脚本说明

### 1. 完整启动脚本
```bash
bash start-dms.sh
```
- 自动启动PostgreSQL数据库
- 自动启动DMS应用程序
- 验证系统状态
- 显示访问地址

### 2. 快速启动脚本
```bash
bash quick-start.sh
```
- 最简化启动流程
- 适合日常快速启动

### 3. 分步启动
```bash
# 仅启动数据库
bash start-database.sh

# 仅启动应用程序
bash start-application.sh
```

### 4. 停止系统
```bash
bash stop-dms.sh
```

## 🌐 访问地址

启动成功后，可通过以下地址访问：

- **本地访问**: http://localhost:8081/dms/login
- **网络访问**: http://**************:8081/dms/login

## 👤 默认登录信息

- 用户名: `admin`
- 密码: `admin123`

## 🔧 系统要求

### 必需软件
- Java 17+
- Maven 3.6+
- PostgreSQL 16.9
- Git Bash

### 路径配置
- Java: D:\JAVA\bin
- Maven: D:\maven\apache-maven-3.9.9
- PostgreSQL: D:\sql\pgsql
- 应用程序: D:\learn\java\dms

## 🛠️ 故障排除

### 1. 数据库启动失败
```bash
# 检查PostgreSQL服务
pgrep -f postgres

# 手动启动数据库
/d/sql/pgsql/bin/pg_ctl -D /d/sql/pgsql/data start
```

### 2. 应用程序启动失败
```bash
# 查看应用日志
tail -f application.log

# 检查端口占用
netstat -ano | findstr :8081

# 重新编译
mvn clean package -DskipTests
```

### 3. 网络访问问题
```bash
# 检查防火墙
netsh advfirewall firewall show rule name=all | findstr 8081

# 检查网络接口
ipconfig
```

## 📊 系统监控

### 健康检查
- http://localhost:8081/dms/api/health
- http://localhost:8081/dms/api/health/simple

### 查看日志
```bash
# 应用程序日志
tail -f application.log

# 数据库日志
tail -f /d/sql/pgsql/data/postgresql.log
```

### 进程管理
```bash
# 查看Java进程
jps -l

# 查看PostgreSQL进程
pgrep -f postgres

# 停止应用程序
pkill -f dms-1.0.0.jar

# 停止数据库
/d/sql/pgsql/bin/pg_ctl -D /d/sql/pgsql/data stop
```

## 🔄 开机自启动（可选）

如需开机自动启动，可以：

1. 将启动脚本添加到Windows启动项
2. 创建Windows服务
3. 使用任务计划程序

## 📞 技术支持

如遇问题，请检查：
1. 所有路径是否正确
2. 环境变量是否配置
3. 端口是否被占用
4. 防火墙设置
5. 日志文件内容
