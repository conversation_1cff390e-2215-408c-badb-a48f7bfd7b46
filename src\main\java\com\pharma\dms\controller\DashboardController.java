package com.pharma.dms.controller;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.service.AuditService;
import com.pharma.dms.service.DocumentService;
import com.pharma.dms.service.UserService;

@RestController
@RequestMapping("/api/dashboard")
@CrossOrigin(origins = "*", maxAge = 3600)
public class DashboardController {

    @Autowired
    private UserService userService;

    @Autowired
    private AuditService auditService;

    @Autowired
    private DocumentService documentService;

    @GetMapping("/stats")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getDashboardStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // User statistics
        stats.put("totalUsers", (long) userService.getAllUsers().size());
        stats.put("activeUsers", userService.getActiveUserCount());
        stats.put("lockedUsers", userService.getLockedUserCount());

        // Document statistics
        stats.put("totalDocuments", documentService.getTotalDocumentCount());
        stats.put("pendingReviews", documentService.getPendingApprovalCount());
        stats.put("draftDocuments", documentService.getDocumentCountByStatus(com.pharma.dms.entity.Document.DocumentStatus.DRAFT));
        stats.put("publishedDocuments", documentService.getDocumentCountByStatus(com.pharma.dms.entity.Document.DocumentStatus.PUBLISHED));
        
        // System statistics
        stats.put("systemAlerts", 2L);
        stats.put("auditLogsToday", auditService.getAuditLogCountSince(LocalDateTime.now().withHour(0).withMinute(0).withSecond(0)));
        
        return ResponseEntity.ok(ApiResponse.success("Dashboard statistics", stats));
    }

    @GetMapping("/recent-activity")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRecentActivity() {
        Map<String, Object> activity = new HashMap<>();
        
        // Get recent audit logs (last 10)
        var recentLogs = auditService.getAllAuditLogs().stream()
                .sorted((a, b) -> b.getTimestamp().compareTo(a.getTimestamp()))
                .limit(10)
                .toList();
        
        activity.put("recentLogs", recentLogs);
        
        return ResponseEntity.ok(ApiResponse.success("Recent activity", activity));
    }

    @GetMapping("/system-status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // System health checks
        status.put("database", "online");
        status.put("fileStorage", "online");
        status.put("backupService", "scheduled");
        status.put("lastBackup", "2024-12-03 23:00:00");
        
        // Performance metrics
        status.put("uptime", "99.9%");
        status.put("responseTime", "120ms");
        status.put("memoryUsage", "65%");
        status.put("diskUsage", "45%");
        
        return ResponseEntity.ok(ApiResponse.success("System status", status));
    }
}
