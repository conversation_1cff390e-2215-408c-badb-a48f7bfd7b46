<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审批流程 - 制药DMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .workflow-step {
            position: relative;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #dee2e6;
            background: #f8f9fa;
            border-radius: 0.375rem;
        }
        .workflow-step.active {
            border-left-color: #0d6efd;
            background: #e7f3ff;
        }
        .workflow-step.completed {
            border-left-color: #198754;
            background: #d1e7dd;
        }
        .workflow-step.rejected {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .approval-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }
        .approval-card:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" style="min-height: 100vh; background-color: #f8f9fa; border-right: 1px solid #dee2e6;">
                <div class="position-sticky pt-3">
                    <div class="navbar-brand mb-4" style="font-weight: bold; color: #0d6efd !important;">
                        <i class="fas fa-pills me-2"></i>制药文档管理系统
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-tachometer-alt me-2"></i>仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/documents" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-file-alt me-2"></i>文档管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/training-courses" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-graduation-cap me-2"></i>培训课程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/approval-workflow" style="background-color: #0d6efd; color: white !important; border-radius: 0.375rem;">
                                <i class="fas fa-route me-2"></i>审批流程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/approval-management" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-check-circle me-2"></i>审批管理
                            </a>
                        </li>
                    </ul>

                    <hr>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/profile" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user me-2"></i>个人资料
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-sign-out-alt me-2"></i>退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-route me-2 text-primary"></i>审批流程管理
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button class="btn btn-outline-primary" onclick="refreshWorkflows()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button class="btn btn-outline-success" onclick="createWorkflow()">
                                <i class="fas fa-plus me-1"></i>创建流程
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 流程类型选项卡 -->
                <ul class="nav nav-pills mb-4" id="workflowTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="document-workflow-tab" data-bs-toggle="pill" data-bs-target="#document-workflow" type="button" role="tab">
                            <i class="fas fa-file-alt me-2"></i>文档审批流程
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="training-workflow-tab" data-bs-toggle="pill" data-bs-target="#training-workflow" type="button" role="tab">
                            <i class="fas fa-graduation-cap me-2"></i>培训审批流程
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="workflow-templates-tab" data-bs-toggle="pill" data-bs-target="#workflow-templates" type="button" role="tab">
                            <i class="fas fa-cogs me-2"></i>流程模板
                        </button>
                    </li>
                </ul>

                <!-- 流程内容 -->
                <div class="tab-content" id="workflowTabContent">
                    <!-- 文档审批流程 -->
                    <div class="tab-pane fade show active" id="document-workflow" role="tabpanel">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-file-alt me-2"></i>文档审批流程
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="workflow-step completed">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <i class="fas fa-upload me-2"></i>1. 文档上传
                                                    </h6>
                                                    <p class="mb-0 text-muted">用户上传文档并填写基本信息</p>
                                                </div>
                                                <span class="badge bg-success">已完成</span>
                                            </div>
                                        </div>

                                        <div class="workflow-step active">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <i class="fas fa-search me-2"></i>2. 初步审查
                                                    </h6>
                                                    <p class="mb-0 text-muted">QA人员进行初步审查和分类</p>
                                                </div>
                                                <span class="badge bg-warning">进行中</span>
                                            </div>
                                        </div>

                                        <div class="workflow-step">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <i class="fas fa-check-circle me-2"></i>3. 正式审批
                                                    </h6>
                                                    <p class="mb-0 text-muted">管理员或QA主管进行正式审批</p>
                                                </div>
                                                <span class="badge bg-secondary">待处理</span>
                                            </div>
                                        </div>

                                        <div class="workflow-step">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <i class="fas fa-globe me-2"></i>4. 发布生效
                                                    </h6>
                                                    <p class="mb-0 text-muted">文档发布并生效，通知相关人员</p>
                                                </div>
                                                <span class="badge bg-secondary">待处理</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-chart-pie me-2"></i>文档审批统计
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-6 mb-3">
                                                <div class="h4 text-primary" id="documentPendingCount">0</div>
                                                <small class="text-muted">待审批</small>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="h4 text-success" id="documentApprovedCount">0</div>
                                                <small class="text-muted">已批准</small>
                                            </div>
                                            <div class="col-6">
                                                <div class="h4 text-danger" id="documentRejectedCount">0</div>
                                                <small class="text-muted">已拒绝</small>
                                            </div>
                                            <div class="col-6">
                                                <div class="h4 text-info" id="documentPublishedCount">0</div>
                                                <small class="text-muted">已发布</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-clock me-2"></i>最近审批活动
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="recentDocumentApprovals">
                                            <div class="text-center text-muted py-3">
                                                <i class="fas fa-spinner fa-spin"></i>
                                                <div class="mt-2">加载中...</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 培训审批流程 -->
                    <div class="tab-pane fade" id="training-workflow" role="tabpanel">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-graduation-cap me-2"></i>培训审批流程
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="workflow-step completed">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <i class="fas fa-plus me-2"></i>1. 课程创建
                                                    </h6>
                                                    <p class="mb-0 text-muted">培训师创建课程内容和大纲</p>
                                                </div>
                                                <span class="badge bg-success">已完成</span>
                                            </div>
                                        </div>

                                        <div class="workflow-step active">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <i class="fas fa-eye me-2"></i>2. 内容审查
                                                    </h6>
                                                    <p class="mb-0 text-muted">QA审查培训内容的准确性和完整性</p>
                                                </div>
                                                <span class="badge bg-warning">进行中</span>
                                            </div>
                                        </div>

                                        <div class="workflow-step">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <i class="fas fa-check-double me-2"></i>3. 正式批准
                                                    </h6>
                                                    <p class="mb-0 text-muted">管理员批准课程并设置生效时间</p>
                                                </div>
                                                <span class="badge bg-secondary">待处理</span>
                                            </div>
                                        </div>

                                        <div class="workflow-step">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <i class="fas fa-play me-2"></i>4. 课程激活
                                                    </h6>
                                                    <p class="mb-0 text-muted">课程激活并开始分配给相关人员</p>
                                                </div>
                                                <span class="badge bg-secondary">待处理</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-chart-bar me-2"></i>培训审批统计
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-6 mb-3">
                                                <div class="h4 text-warning" id="trainingPendingCount">0</div>
                                                <small class="text-muted">待审批</small>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="h4 text-success" id="trainingApprovedCount">0</div>
                                                <small class="text-muted">已批准</small>
                                            </div>
                                            <div class="col-6">
                                                <div class="h4 text-info" id="trainingActiveCount">0</div>
                                                <small class="text-muted">已激活</small>
                                            </div>
                                            <div class="col-6">
                                                <div class="h4 text-secondary" id="trainingDraftCount">0</div>
                                                <small class="text-muted">草稿</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-history me-2"></i>最近培训审批
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="recentTrainingApprovals">
                                            <div class="text-center text-muted py-3">
                                                <i class="fas fa-spinner fa-spin"></i>
                                                <div class="mt-2">加载中...</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 流程模板 -->
                    <div class="tab-pane fade" id="workflow-templates" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-cogs me-2"></i>审批流程模板
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <div class="card approval-card">
                                            <div class="card-body text-center">
                                                <i class="fas fa-file-alt fa-3x text-primary mb-3"></i>
                                                <h6>标准文档审批</h6>
                                                <p class="text-muted small">适用于一般文档的标准审批流程</p>
                                                <button class="btn btn-outline-primary btn-sm">使用模板</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="card approval-card">
                                            <div class="card-body text-center">
                                                <i class="fas fa-shield-alt fa-3x text-danger mb-3"></i>
                                                <h6>GMP文档审批</h6>
                                                <p class="text-muted small">符合GMP要求的严格审批流程</p>
                                                <button class="btn btn-outline-danger btn-sm">使用模板</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="card approval-card">
                                            <div class="card-body text-center">
                                                <i class="fas fa-graduation-cap fa-3x text-success mb-3"></i>
                                                <h6>培训课程审批</h6>
                                                <p class="text-muted small">培训课程的专用审批流程</p>
                                                <button class="btn btn-outline-success btn-sm">使用模板</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/dms/js/auth.js"></script>
    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadWorkflowStats();
            loadRecentApprovals();
        });

        async function loadWorkflowStats() {
            try {
                // 加载文档审批统计
                const docResponse = await authUtils.secureApiCall('/dms/api/documents/approval-stats');
                if (docResponse.ok) {
                    const docStats = await docResponse.json();
                    updateDocumentStats(docStats.data);
                }

                // 加载培训审批统计
                const trainingResponse = await authUtils.secureApiCall('/dms/api/training-courses/approval-stats');
                if (trainingResponse.ok) {
                    const trainingStats = await trainingResponse.json();
                    updateTrainingStats(trainingStats.data);
                }
            } catch (error) {
                console.error('加载审批统计失败:', error);
            }
        }

        function updateDocumentStats(stats) {
            document.getElementById('documentPendingCount').textContent = stats.pending || 0;
            document.getElementById('documentApprovedCount').textContent = stats.approved || 0;
            document.getElementById('documentRejectedCount').textContent = stats.rejected || 0;
            document.getElementById('documentPublishedCount').textContent = stats.published || 0;
        }

        function updateTrainingStats(stats) {
            document.getElementById('trainingPendingCount').textContent = stats.pending || 0;
            document.getElementById('trainingApprovedCount').textContent = stats.approved || 0;
            document.getElementById('trainingActiveCount').textContent = stats.active || 0;
            document.getElementById('trainingDraftCount').textContent = stats.draft || 0;
        }

        async function loadRecentApprovals() {
            try {
                // 加载最近的文档审批
                const docResponse = await authUtils.secureApiCall('/dms/api/document-approvals/recent?limit=5');
                if (docResponse.ok) {
                    const docApprovals = await docResponse.json();
                    displayRecentDocumentApprovals(docApprovals.data);
                }

                // 加载最近的培训审批
                const trainingResponse = await authUtils.secureApiCall('/dms/api/training-courses/recent-approvals?limit=5');
                if (trainingResponse.ok) {
                    const trainingApprovals = await trainingResponse.json();
                    displayRecentTrainingApprovals(trainingApprovals.data);
                }
            } catch (error) {
                console.error('加载最近审批失败:', error);
                document.getElementById('recentDocumentApprovals').innerHTML = 
                    '<div class="text-center text-muted">暂无数据</div>';
                document.getElementById('recentTrainingApprovals').innerHTML = 
                    '<div class="text-center text-muted">暂无数据</div>';
            }
        }

        function displayRecentDocumentApprovals(approvals) {
            const container = document.getElementById('recentDocumentApprovals');
            if (approvals.length === 0) {
                container.innerHTML = '<div class="text-center text-muted">暂无审批记录</div>';
                return;
            }

            let html = '';
            approvals.forEach(approval => {
                html += `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <div class="fw-bold small">${approval.documentTitle}</div>
                            <div class="text-muted small">${approval.approverName}</div>
                        </div>
                        <span class="badge bg-${approval.status === 'APPROVED' ? 'success' : 'danger'} small">
                            ${approval.status}
                        </span>
                    </div>
                `;
            });
            container.innerHTML = html;
        }

        function displayRecentTrainingApprovals(approvals) {
            const container = document.getElementById('recentTrainingApprovals');
            if (approvals.length === 0) {
                container.innerHTML = '<div class="text-center text-muted">暂无审批记录</div>';
                return;
            }

            let html = '';
            approvals.forEach(approval => {
                html += `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <div class="fw-bold small">${approval.courseTitle}</div>
                            <div class="text-muted small">${approval.approverName}</div>
                        </div>
                        <span class="badge bg-${approval.status === 'APPROVED' ? 'success' : 'warning'} small">
                            ${approval.status}
                        </span>
                    </div>
                `;
            });
            container.innerHTML = html;
        }

        function refreshWorkflows() {
            loadWorkflowStats();
            loadRecentApprovals();
            alert('审批流程数据已刷新');
        }

        function createWorkflow() {
            alert('创建自定义审批流程功能开发中');
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }
    </script>
</body>
</html>
