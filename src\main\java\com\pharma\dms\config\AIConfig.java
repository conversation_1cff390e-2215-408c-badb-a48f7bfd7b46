package com.pharma.dms.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.cache.annotation.EnableCaching;

@Configuration
@EnableCaching
public class AIConfig {

    @Bean
    @ConfigurationProperties(prefix = "ai.openrouter")
    public OpenRouterProperties openRouterProperties() {
        return new OpenRouterProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai.ocr")
    public OCRProperties ocrProperties() {
        return new OCRProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "ai.features")
    public AIFeatureProperties aiFeatureProperties() {
        return new AIFeatureProperties();
    }

    @Bean
    public WebClient openRouterWebClient(OpenRouterProperties properties) {
        return WebClient.builder()
                .baseUrl(properties.getBaseUrl())
                .defaultHeader("Authorization", "Bearer " + properties.getApiKey())
                .defaultHeader("Content-Type", "application/json")
                .defaultHeader("HTTP-Referer", "https://pharma-dms.local")
                .defaultHeader("X-Title", "Pharmaceutical DMS")
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
                .build();
    }

    public static class OpenRouterProperties {
        private String apiKey;
        private String baseUrl;
        private String model;
        private Integer maxTokens;
        private Double temperature;
        private Long timeout;

        // Getters and Setters
        public String getApiKey() { return apiKey; }
        public void setApiKey(String apiKey) { this.apiKey = apiKey; }
        public String getBaseUrl() { return baseUrl; }
        public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }
        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }
        public Integer getMaxTokens() { return maxTokens; }
        public void setMaxTokens(Integer maxTokens) { this.maxTokens = maxTokens; }
        public Double getTemperature() { return temperature; }
        public void setTemperature(Double temperature) { this.temperature = temperature; }
        public Long getTimeout() { return timeout; }
        public void setTimeout(Long timeout) { this.timeout = timeout; }
    }

    public static class OCRProperties {
        private Boolean enabled;
        private String tesseractPath;
        private String language;
        private Integer confidenceThreshold;

        // Getters and Setters
        public Boolean getEnabled() { return enabled; }
        public void setEnabled(Boolean enabled) { this.enabled = enabled; }
        public String getTesseractPath() { return tesseractPath; }
        public void setTesseractPath(String tesseractPath) { this.tesseractPath = tesseractPath; }
        public String getLanguage() { return language; }
        public void setLanguage(String language) { this.language = language; }
        public Integer getConfidenceThreshold() { return confidenceThreshold; }
        public void setConfidenceThreshold(Integer confidenceThreshold) { this.confidenceThreshold = confidenceThreshold; }
    }

    public static class AIFeatureProperties {
        private Boolean documentAnalysis;
        private Boolean contentExtraction;
        private Boolean complianceCheck;
        private Boolean autoCategorization;

        // Getters and Setters
        public Boolean getDocumentAnalysis() { return documentAnalysis; }
        public void setDocumentAnalysis(Boolean documentAnalysis) { this.documentAnalysis = documentAnalysis; }
        public Boolean getContentExtraction() { return contentExtraction; }
        public void setContentExtraction(Boolean contentExtraction) { this.contentExtraction = contentExtraction; }
        public Boolean getComplianceCheck() { return complianceCheck; }
        public void setComplianceCheck(Boolean complianceCheck) { this.complianceCheck = complianceCheck; }
        public Boolean getAutoCategorization() { return autoCategorization; }
        public void setAutoCategorization(Boolean autoCategorization) { this.autoCategorization = autoCategorization; }
    }
}
