<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token测试 - 制药DMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Token测试页面</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Token状态</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Token存在:</strong> <span id="tokenStatus">检查中...</span></p>
                        <p><strong>Token内容:</strong> <span id="tokenContent">检查中...</span></p>
                        <p><strong>用户信息:</strong> <span id="userInfo">检查中...</span></p>
                        <button class="btn btn-primary" onclick="checkTokenStatus()">检查Token</button>
                        <button class="btn btn-warning" onclick="clearAllTokens()">清除Token</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>导航测试</h5>
                    </div>
                    <div class="card-body">
                        <p>测试页面跳转是否会清除token：</p>
                        <a href="/dms/system-overview" class="btn btn-info mb-2">系统概览</a><br>
                        <a href="/dms/documents" class="btn btn-info mb-2">文档管理</a><br>
                        <a href="/dms/users" class="btn btn-info mb-2">用户管理</a><br>
                        <a href="/dms/reports" class="btn btn-info mb-2">报表管理</a><br>
                        <a href="/dms/settings" class="btn btn-info mb-2">系统设置</a><br>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>手动登录测试</h5>
                    </div>
                    <div class="card-body">
                        <form id="loginForm">
                            <div class="row">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" id="username" placeholder="用户名" value="admin">
                                </div>
                                <div class="col-md-4">
                                    <input type="password" class="form-control" id="password" placeholder="密码" value="admin123">
                                </div>
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-success" onclick="testLogin()">测试登录</button>
                                </div>
                            </div>
                        </form>
                        <div id="loginResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function checkTokenStatus() {
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            
            document.getElementById('tokenStatus').textContent = token ? '存在' : '不存在';
            document.getElementById('tokenContent').textContent = token ? token.substring(0, 50) + '...' : '无';
            
            if (user) {
                try {
                    const userObj = JSON.parse(user);
                    document.getElementById('userInfo').textContent = userObj.username || '解析失败';
                } catch (e) {
                    document.getElementById('userInfo').textContent = '解析错误';
                }
            } else {
                document.getElementById('userInfo').textContent = '无';
            }
        }

        function clearAllTokens() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            checkTokenStatus();
            alert('Token已清除');
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/dms/api/auth/signin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    // 保存token和用户信息
                    localStorage.setItem('token', result.data.token);
                    localStorage.setItem('user', JSON.stringify(result.data.user));
                    
                    document.getElementById('loginResult').innerHTML = 
                        '<div class="alert alert-success">登录成功！Token已保存</div>';
                    
                    checkTokenStatus();
                } else {
                    document.getElementById('loginResult').innerHTML = 
                        '<div class="alert alert-danger">登录失败：' + result.message + '</div>';
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    '<div class="alert alert-danger">登录错误：' + error.message + '</div>';
            }
        }

        // 页面加载时检查token状态
        document.addEventListener('DOMContentLoaded', function() {
            checkTokenStatus();
        });
    </script>
</body>
</html>
