package com.pharma.dms.service;

import com.pharma.dms.entity.AuditLog;
import com.pharma.dms.entity.User;
import com.pharma.dms.repository.AuditLogRepository;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class AuditService {

    @Autowired
    private AuditLogRepository auditLogRepository;

    public void logUserCreation(User user) {
        logAuditEvent("USER_CREATED", "User", user.getId(), 
                     null, getUserInfo(user), AuditLog.Severity.INFO,
                     "User account created: " + user.getUsername());
    }

    public void logUserUpdate(User user, String oldValues, String newValues) {
        logAuditEvent("USER_UPDATED", "User", user.getId(), 
                     oldValues, newValues, AuditLog.Severity.INFO,
                     "User account updated: " + user.getUsername());
    }

    public void logUserDeletion(User user) {
        logAuditEvent("USER_DELETED", "User", user.getId(), 
                     getUserInfo(user), null, AuditLog.Severity.WARNING,
                     "User account deactivated: " + user.getUsername());
    }

    public void logUserActivation(User user) {
        logAuditEvent("USER_ACTIVATED", "User", user.getId(), 
                     null, getUserInfo(user), AuditLog.Severity.INFO,
                     "User account activated: " + user.getUsername());
    }

    public void logUserLock(User user) {
        logAuditEvent("USER_LOCKED", "User", user.getId(), 
                     null, getUserInfo(user), AuditLog.Severity.WARNING,
                     "User account locked: " + user.getUsername());
    }

    public void logUserUnlock(User user) {
        logAuditEvent("USER_UNLOCKED", "User", user.getId(), 
                     null, getUserInfo(user), AuditLog.Severity.INFO,
                     "User account unlocked: " + user.getUsername());
    }

    public void logUserAutoLock(User user) {
        logAuditEvent("USER_AUTO_LOCKED", "User", user.getId(), 
                     null, getUserInfo(user), AuditLog.Severity.ERROR,
                     "User account auto-locked due to failed login attempts: " + user.getUsername());
    }

    public void logPasswordChange(User user) {
        logAuditEvent("PASSWORD_CHANGED", "User", user.getId(), 
                     null, null, AuditLog.Severity.INFO,
                     "Password changed for user: " + user.getUsername());
    }

    public void logLogin(String username, boolean success) {
        AuditLog.Severity severity = success ? AuditLog.Severity.INFO : AuditLog.Severity.WARNING;
        String action = success ? "LOGIN_SUCCESS" : "LOGIN_FAILED";
        String details = success ? "User logged in successfully" : "Failed login attempt";
        
        logAuditEvent(action, "Authentication", null, 
                     null, null, severity, details, username);
    }

    public void logLogout(String username) {
        logAuditEvent("LOGOUT", "Authentication", null, 
                     null, null, AuditLog.Severity.INFO,
                     "User logged out", username);
    }

    public void logSystemEvent(String action, String details, AuditLog.Severity severity) {
        logAuditEvent(action, "System", null, 
                     null, null, severity, details, "SYSTEM");
    }

    private void logAuditEvent(String action, String entityType, Long entityId,
                              String oldValues, String newValues, AuditLog.Severity severity,
                              String details) {
        String currentUsername = getCurrentUsername();
        logAuditEvent(action, entityType, entityId, oldValues, newValues, 
                     severity, details, currentUsername);
    }

    private void logAuditEvent(String action, String entityType, Long entityId,
                              String oldValues, String newValues, AuditLog.Severity severity,
                              String details, String username) {
        try {
            AuditLog auditLog = new AuditLog();
            auditLog.setUsername(username);
            auditLog.setAction(action);
            auditLog.setEntityType(entityType);
            auditLog.setEntityId(entityId);
            auditLog.setOldValues(oldValues);
            auditLog.setNewValues(newValues);
            auditLog.setSeverity(severity);
            auditLog.setDetails(details);
            auditLog.setTimestamp(LocalDateTime.now());

            // Get request information if available
            try {
                ServletRequestAttributes attributes = 
                    (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                if (attributes != null) {
                    HttpServletRequest request = attributes.getRequest();
                    auditLog.setIpAddress(getClientIpAddress(request));
                    auditLog.setUserAgent(request.getHeader("User-Agent"));
                }
            } catch (Exception e) {
                // Ignore if request context is not available
            }

            auditLogRepository.save(auditLog);
        } catch (Exception e) {
            // Log to system logger if audit logging fails
            System.err.println("Failed to log audit event: " + e.getMessage());
        }
    }

    private String getCurrentUsername() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated() 
                && !"anonymousUser".equals(authentication.getPrincipal())) {
                return authentication.getName();
            }
        } catch (Exception e) {
            // Ignore
        }
        return "SYSTEM";
    }

    private String getUserInfo(User user) {
        return String.format("ID: %d, Username: %s, Email: %s, Name: %s %s", 
                           user.getId(), user.getUsername(), user.getEmail(), 
                           user.getFirstName(), user.getLastName());
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    // Query methods
    public List<AuditLog> getAllAuditLogs() {
        return auditLogRepository.findAll();
    }

    public Page<AuditLog> getAllAuditLogs(Pageable pageable) {
        return auditLogRepository.findAll(pageable);
    }

    public List<AuditLog> getAuditLogsByUsername(String username) {
        return auditLogRepository.findByUsername(username);
    }

    public List<AuditLog> getAuditLogsByAction(String action) {
        return auditLogRepository.findByAction(action);
    }

    public List<AuditLog> getAuditLogsByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return auditLogRepository.findByTimestampBetween(startDate, endDate);
    }

    public Page<AuditLog> searchAuditLogs(String username, String action, String entityType,
                                         AuditLog.Severity severity, LocalDateTime startDate,
                                         LocalDateTime endDate, Pageable pageable) {
        return auditLogRepository.findAuditLogsWithFilters(username, action, entityType,
                                                          severity, startDate, endDate, pageable);
    }

    public long getAuditLogCount() {
        return auditLogRepository.count();
    }

    public long getAuditLogCountSince(LocalDateTime date) {
        return auditLogRepository.countAuditLogsSince(date);
    }
}
