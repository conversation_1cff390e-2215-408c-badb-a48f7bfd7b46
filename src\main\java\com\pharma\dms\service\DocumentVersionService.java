package com.pharma.dms.service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.pharma.dms.entity.Document;
import com.pharma.dms.entity.DocumentVersion;
import com.pharma.dms.entity.User;
import com.pharma.dms.repository.DocumentVersionRepository;

@Service
@Transactional
public class DocumentVersionService {

    @Autowired
    private DocumentVersionRepository documentVersionRepository;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private UserService userService;

    private static final String UPLOAD_DIR = "uploads/versions/";

    /**
     * 创建新版本
     */
    public DocumentVersion createVersion(Document document, MultipartFile file, 
                                       DocumentVersion.ChangeType changeType, 
                                       String changeDescription, String changeReason,
                                       User createdBy) throws IOException {
        
        // 获取当前版本
        Optional<DocumentVersion> currentVersionOpt = getCurrentVersion(document);
        String newVersionNumber;
        
        if (currentVersionOpt.isPresent()) {
            DocumentVersion currentVersion = currentVersionOpt.get();
            newVersionNumber = DocumentVersion.generateNextVersion(
                currentVersion.getVersionNumber(), changeType);
            
            // 标记当前版本为非当前版本
            currentVersion.setIsCurrent(false);
            if (changeType == DocumentVersion.ChangeType.MAJOR || 
                changeType == DocumentVersion.ChangeType.MINOR) {
                currentVersion.setSupersededDate(LocalDateTime.now());
                currentVersion.setStatus(DocumentVersion.VersionStatus.SUPERSEDED);
            }
            documentVersionRepository.save(currentVersion);
        } else {
            newVersionNumber = "1.0.0";
        }

        // 创建新版本
        DocumentVersion newVersion = new DocumentVersion(document, newVersionNumber, createdBy);
        newVersion.setChangeType(changeType);
        newVersion.setChangeDescription(changeDescription);
        newVersion.setChangeReason(changeReason);
        newVersion.setIsCurrent(true);
        
        // 保存文件
        String fileName = saveVersionFile(file, document.getId(), newVersionNumber);
        newVersion.setFilePath(fileName);
        newVersion.setFileSize(file.getSize());
        newVersion.setMimeType(file.getContentType());
        newVersion.setChecksum(calculateChecksum(file.getBytes()));
        
        // 设置GMP相关字段
        if (newVersion.requiresGmpAssessment()) {
            newVersion.setTrainingRequired(true);
        }
        
        return documentVersionRepository.save(newVersion);
    }

    /**
     * 根据ID获取版本
     */
    public Optional<DocumentVersion> getVersionById(Long versionId) {
        return documentVersionRepository.findById(versionId);
    }

    /**
     * 获取文档的当前版本
     */
    public Optional<DocumentVersion> getCurrentVersion(Document document) {
        return documentVersionRepository.findByDocumentAndIsCurrent(document, true);
    }

    /**
     * 获取文档的所有版本
     */
    public List<DocumentVersion> getVersionHistory(Document document) {
        return documentVersionRepository.findByDocumentOrderByCreatedAtDesc(document);
    }

    /**
     * 获取文档的所有版本（分页）
     */
    public Page<DocumentVersion> getVersionHistory(Document document, Pageable pageable) {
        return documentVersionRepository.findByDocumentOrderByCreatedAtDesc(document, pageable);
    }

    /**
     * 根据版本号获取版本
     */
    public Optional<DocumentVersion> getVersionByNumber(Document document, String versionNumber) {
        return documentVersionRepository.findByDocumentAndVersionNumber(document, versionNumber);
    }

    /**
     * 批准版本
     */
    public DocumentVersion approveVersion(Long versionId, User approvedBy) {
        DocumentVersion version = documentVersionRepository.findById(versionId)
                .orElseThrow(() -> new RuntimeException("Version not found"));
        
        version.setStatus(DocumentVersion.VersionStatus.APPROVED);
        version.setApprovedBy(approvedBy);
        version.setApprovedAt(LocalDateTime.now());
        
        return documentVersionRepository.save(version);
    }

    /**
     * 激活版本
     */
    public DocumentVersion activateVersion(Long versionId, User activatedBy) {
        DocumentVersion version = documentVersionRepository.findById(versionId)
                .orElseThrow(() -> new RuntimeException("Version not found"));
        
        if (version.getStatus() != DocumentVersion.VersionStatus.APPROVED) {
            throw new RuntimeException("Only approved versions can be activated");
        }
        
        // 取消当前活跃版本
        Optional<DocumentVersion> currentVersionOpt = getCurrentVersion(version.getDocument());
        if (currentVersionOpt.isPresent()) {
            DocumentVersion currentVersion = currentVersionOpt.get();
            currentVersion.setIsCurrent(false);
            currentVersion.setSupersededDate(LocalDateTime.now());
            currentVersion.setStatus(DocumentVersion.VersionStatus.SUPERSEDED);
            documentVersionRepository.save(currentVersion);
        }
        
        // 激活新版本
        version.setStatus(DocumentVersion.VersionStatus.ACTIVE);
        version.setIsCurrent(true);
        version.setEffectiveDate(LocalDateTime.now());
        
        return documentVersionRepository.save(version);
    }

    /**
     * 归档版本
     */
    public DocumentVersion archiveVersion(Long versionId, User archivedBy) {
        DocumentVersion version = documentVersionRepository.findById(versionId)
                .orElseThrow(() -> new RuntimeException("Version not found"));
        
        if (version.getIsCurrent()) {
            throw new RuntimeException("Cannot archive current version");
        }
        
        version.setIsArchived(true);
        version.setStatus(DocumentVersion.VersionStatus.ARCHIVED);
        
        // 设置保留期限（例如7年）
        version.setRetentionDate(LocalDateTime.now().plusYears(7));
        
        return documentVersionRepository.save(version);
    }

    /**
     * 比较两个版本
     */
    public int compareVersions(DocumentVersion version1, DocumentVersion version2) {
        return version1.compareVersion(version2);
    }

    /**
     * 获取版本统计信息
     */
    public VersionStatistics getVersionStatistics(Document document) {
        List<DocumentVersion> versions = getVersionHistory(document);
        
        long totalVersions = versions.size();
        long majorVersions = versions.stream()
                .filter(v -> v.getChangeType() == DocumentVersion.ChangeType.MAJOR)
                .count();
        long minorVersions = versions.stream()
                .filter(v -> v.getChangeType() == DocumentVersion.ChangeType.MINOR)
                .count();
        long patchVersions = versions.stream()
                .filter(v -> v.getChangeType() == DocumentVersion.ChangeType.PATCH)
                .count();
        
        Optional<DocumentVersion> latestVersion = versions.stream().findFirst();
        
        return new VersionStatistics(totalVersions, majorVersions, minorVersions, 
                                   patchVersions, latestVersion.orElse(null));
    }

    /**
     * 获取需要GMP评估的版本
     */
    public List<DocumentVersion> getVersionsRequiringGmpAssessment() {
        return documentVersionRepository.findVersionsRequiringGmpReview();
    }

    /**
     * 更新GMP影响评估
     */
    public DocumentVersion updateGmpAssessment(Long versionId, String assessment, 
                                             boolean trainingRequired, User assessor) {
        DocumentVersion version = documentVersionRepository.findById(versionId)
                .orElseThrow(() -> new RuntimeException("Version not found"));
        
        version.setGmpImpactAssessment(assessment);
        version.setTrainingRequired(trainingRequired);
        
        return documentVersionRepository.save(version);
    }

    /**
     * 获取即将到期的版本
     */
    public List<DocumentVersion> getVersionsNearingRetention(int daysBeforeExpiry) {
        LocalDateTime cutoffDate = LocalDateTime.now().plusDays(daysBeforeExpiry);
        return documentVersionRepository.findByRetentionDateBefore(cutoffDate);
    }

    /**
     * 删除过期版本
     */
    public void deleteExpiredVersions() {
        List<DocumentVersion> expiredVersions = 
                documentVersionRepository.findVersionsReadyForDeletion(LocalDateTime.now());
        
        for (DocumentVersion version : expiredVersions) {
            // 删除文件
            try {
                Path filePath = Paths.get(UPLOAD_DIR, version.getFilePath());
                Files.deleteIfExists(filePath);
            } catch (IOException e) {
                // 记录日志但继续处理
                System.err.println("Failed to delete file: " + version.getFilePath());
            }
            
            // 删除数据库记录
            documentVersionRepository.delete(version);
        }
    }

    /**
     * 保存版本文件
     */
    private String saveVersionFile(MultipartFile file, Long documentId, String versionNumber) 
            throws IOException {
        
        // 创建目录
        Path uploadPath = Paths.get(UPLOAD_DIR);
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
        }
        
        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String fileName = "doc_" + documentId + "_v" + versionNumber + "_" + 
                         System.currentTimeMillis() + extension;
        
        // 保存文件
        Path filePath = uploadPath.resolve(fileName);
        Files.copy(file.getInputStream(), filePath);
        
        return fileName;
    }

    /**
     * 计算文件校验和
     */
    private String calculateChecksum(byte[] data) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(data);
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not available", e);
        }
    }

    /**
     * 版本统计信息类
     */
    public static class VersionStatistics {
        private final long totalVersions;
        private final long majorVersions;
        private final long minorVersions;
        private final long patchVersions;
        private final DocumentVersion latestVersion;

        public VersionStatistics(long totalVersions, long majorVersions, 
                               long minorVersions, long patchVersions, 
                               DocumentVersion latestVersion) {
            this.totalVersions = totalVersions;
            this.majorVersions = majorVersions;
            this.minorVersions = minorVersions;
            this.patchVersions = patchVersions;
            this.latestVersion = latestVersion;
        }

        // Getters
        public long getTotalVersions() { return totalVersions; }
        public long getMajorVersions() { return majorVersions; }
        public long getMinorVersions() { return minorVersions; }
        public long getPatchVersions() { return patchVersions; }
        public DocumentVersion getLatestVersion() { return latestVersion; }
    }
}
