#!/bin/bash

echo "========================================"
echo "Pharmaceutical DMS - Starting Application"
echo "========================================"

echo ""
echo "Checking Java installation..."
java -version
if [ $? -ne 0 ]; then
    echo "ERROR: Java not found. Please ensure Java 17+ is installed and in PATH."
    exit 1
fi

echo ""
echo "Checking Maven installation..."
mvn -version
if [ $? -ne 0 ]; then
    echo "ERROR: Maven not found. Please ensure <PERSON>ven is installed and in PATH."
    exit 1
fi

echo ""
echo "Starting PostgreSQL (if not already running)..."
echo "Please ensure PostgreSQL is running on localhost:5432"

echo ""
echo "Building and starting the application..."
echo "Profile: postgresql"
echo "Port: 8081"
echo "Context Path: /dms"

echo ""
echo "Starting application..."
mvn spring-boot:run "-Dspring-boot.run.profiles=postgresql"

echo ""
echo "Application stopped."
