package com.pharma.dms.controller;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.TrainingRecord;
import com.pharma.dms.entity.User;
import com.pharma.dms.security.UserPrincipal;
import com.pharma.dms.service.TrainingRecordService;
import com.pharma.dms.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/training/records")
@CrossOrigin(origins = "*", maxAge = 3600)
public class TrainingRecordController {

    @Autowired
    private TrainingRecordService recordService;

    @Autowired
    private UserService userService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Page<TrainingRecord>>> getAllRecords(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : 
                   Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<TrainingRecord> records = recordService.getAllRecords(pageable);
        
        return ResponseEntity.ok(ApiResponse.success("Training records retrieved successfully", records));
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<TrainingRecord>> getRecordById(@PathVariable Long id,
                                                                   @AuthenticationPrincipal UserPrincipal userPrincipal) {
        Optional<TrainingRecord> record = recordService.getRecordById(id);
        
        if (record.isPresent()) {
            TrainingRecord tr = record.get();
            User currentUser = userService.getUserByUsername(userPrincipal.getUsername()).orElse(null);
            
            // Check if user can access this record
            if (currentUser != null && 
                (tr.getUser().getId().equals(currentUser.getId()) || 
                 currentUser.getRoles().stream().anyMatch(role -> 
                     role.getName().name().equals("ROLE_ADMIN") || role.getName().name().equals("ROLE_QA")))) {
                return ResponseEntity.ok(ApiResponse.success("Training record found", tr));
            } else {
                return ResponseEntity.status(403)
                        .body(ApiResponse.error("Access denied", "Insufficient permissions"));
            }
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/my-records")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<TrainingRecord>>> getMyRecords(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            User user = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            List<TrainingRecord> records = recordService.getRecordsByUser(user);
            return ResponseEntity.ok(ApiResponse.success("User training records retrieved", records));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get training records", e.getMessage()));
        }
    }

    @PostMapping("/start/{courseId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<TrainingRecord>> startTraining(
            @PathVariable Long courseId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            User user = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            TrainingRecord record = recordService.startTraining(courseId, user.getId());
            return ResponseEntity.ok(ApiResponse.success("Training started successfully", record));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to start training", e.getMessage()));
        }
    }

    @PostMapping("/{recordId}/complete")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<TrainingRecord>> completeTraining(
            @PathVariable Long recordId,
            @RequestBody Map<String, Object> completionData,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            User user = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            // Verify user owns this record
            Optional<TrainingRecord> recordOpt = recordService.getRecordById(recordId);
            if (recordOpt.isEmpty() || !recordOpt.get().getUser().getId().equals(user.getId())) {
                return ResponseEntity.status(403)
                        .body(ApiResponse.error("Access denied", "Cannot complete training for another user"));
            }
            
            Integer finalScore = (Integer) completionData.get("finalScore");
            String feedback = (String) completionData.get("feedback");
            
            if (finalScore == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Invalid data", "Final score is required"));
            }
            
            TrainingRecord record = recordService.completeTraining(recordId, finalScore, feedback);
            return ResponseEntity.ok(ApiResponse.success("Training completed successfully", record));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to complete training", e.getMessage()));
        }
    }

    @PostMapping("/{recordId}/sign")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<TrainingRecord>> addElectronicSignature(
            @PathVariable Long recordId,
            @RequestBody Map<String, String> signatureData,
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            HttpServletRequest request) {
        try {
            User user = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            // Verify user owns this record
            Optional<TrainingRecord> recordOpt = recordService.getRecordById(recordId);
            if (recordOpt.isEmpty() || !recordOpt.get().getUser().getId().equals(user.getId())) {
                return ResponseEntity.status(403)
                        .body(ApiResponse.error("Access denied", "Cannot sign training for another user"));
            }
            
            String signature = signatureData.get("signature");
            if (signature == null || signature.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Invalid data", "Electronic signature is required"));
            }
            
            String ipAddress = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            
            TrainingRecord record = recordService.addElectronicSignature(recordId, signature, ipAddress, userAgent);
            return ResponseEntity.ok(ApiResponse.success("Electronic signature added successfully", record));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to add electronic signature", e.getMessage()));
        }
    }

    @GetMapping("/certificate/{certificateNumber}/validate")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Boolean>> validateCertificate(@PathVariable String certificateNumber) {
        boolean isValid = recordService.validateCertificate(certificateNumber);
        return ResponseEntity.ok(ApiResponse.success("Certificate validation completed", isValid));
    }

    @PostMapping("/{recordId}/schedule-retrain")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Void>> scheduleRetrain(
            @PathVariable Long recordId,
            @RequestBody Map<String, Object> retrainData) {
        try {
            String reason = (String) retrainData.get("reason");
            String dueDateStr = (String) retrainData.get("dueDate");
            
            if (reason == null || reason.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Invalid data", "Retrain reason is required"));
            }
            
            LocalDateTime dueDate = dueDateStr != null ? 
                    LocalDateTime.parse(dueDateStr) : 
                    LocalDateTime.now().plusDays(30);
            
            recordService.scheduleRetrain(recordId, reason, dueDate);
            return ResponseEntity.ok(ApiResponse.success("Retrain scheduled successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to schedule retrain", e.getMessage()));
        }
    }

    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Page<TrainingRecord>>> searchRecords(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long courseId,
            @RequestParam(required = false) TrainingRecord.TrainingStatus status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) Long instructorId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : 
                   Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        
        LocalDateTime start = startDate != null ? LocalDateTime.parse(startDate) : null;
        LocalDateTime end = endDate != null ? LocalDateTime.parse(endDate) : null;
        
        Page<TrainingRecord> records = recordService.searchRecords(userId, courseId, status,
                start, end, instructorId, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("Search completed", records));
    }

    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRecordStats() {
        Map<String, Object> stats = Map.of(
            "totalRecords", recordService.getTotalRecordCount(),
            "completedRecords", recordService.getRecordCountByStatus(TrainingRecord.TrainingStatus.COMPLETED),
            "inProgressRecords", recordService.getRecordCountByStatus(TrainingRecord.TrainingStatus.IN_PROGRESS),
            "certifiedRecords", recordService.getRecordCountByStatus(TrainingRecord.TrainingStatus.CERTIFIED),
            "retrainRequired", recordService.getRetrainRequired().size(),
            "overdueRetraining", recordService.getOverdueRetraining().size()
        );
        
        return ResponseEntity.ok(ApiResponse.success("Training record statistics", stats));
    }

    @GetMapping("/expiring-certificates")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<List<TrainingRecord>>> getExpiringCertificates(
            @RequestParam(defaultValue = "30") int days) {
        
        List<TrainingRecord> expiringCertificates = recordService.getExpiringCertificates(days);
        return ResponseEntity.ok(ApiResponse.success("Expiring certificates retrieved", expiringCertificates));
    }

    @GetMapping("/retrain-required")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<List<TrainingRecord>>> getRetrainRequired() {
        List<TrainingRecord> retrainRequired = recordService.getRetrainRequired();
        return ResponseEntity.ok(ApiResponse.success("Retrain required records retrieved", retrainRequired));
    }

    @GetMapping("/overdue-retraining")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<List<TrainingRecord>>> getOverdueRetraining() {
        List<TrainingRecord> overdueRetraining = recordService.getOverdueRetraining();
        return ResponseEntity.ok(ApiResponse.success("Overdue retraining records retrieved", overdueRetraining));
    }

    // Utility method to get client IP address
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedForHeader = request.getHeader("X-Forwarded-For");
        if (xForwardedForHeader == null) {
            return request.getRemoteAddr();
        } else {
            return xForwardedForHeader.split(",")[0];
        }
    }
}
