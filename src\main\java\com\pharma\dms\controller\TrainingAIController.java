package com.pharma.dms.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.Document;
import com.pharma.dms.service.DocumentService;
import com.pharma.dms.service.OpenRouterAIService;

@RestController
@RequestMapping("/api/training-ai")
@CrossOrigin(origins = "*")
public class TrainingAIController {

    @Autowired
    private OpenRouterAIService aiService;

    @Autowired
    private DocumentService documentService;

    /**
     * 根据上传的文档生成培训大纲
     */
    @PostMapping("/generate-outline")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> generateTrainingOutline(
            @RequestParam("file") MultipartFile file,
            @RequestParam("courseTitle") String courseTitle) {
        try {
            System.out.println("=== AI生成培训大纲 ===");
            System.out.println("文件名: " + file.getOriginalFilename());
            System.out.println("课程标题: " + courseTitle);

            // 提取文档内容
            String documentContent = extractDocumentContent(file);
            if (documentContent.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("无法提取文档内容", null));
            }

            System.out.println("文档内容长度: " + documentContent.length());

            // 调用AI生成大纲
            String outline = aiService.generateTrainingOutline(documentContent, courseTitle)
                    .block(); // 同步调用

            Map<String, Object> result = new HashMap<>();
            result.put("outline", outline);
            result.put("documentLength", documentContent.length());
            result.put("fileName", file.getOriginalFilename());

            System.out.println("✅ 培训大纲生成成功");
            return ResponseEntity.ok(ApiResponse.success("培训大纲生成成功", result));

        } catch (Exception e) {
            System.err.println("❌ 生成培训大纲失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("生成培训大纲失败", e.getMessage()));
        }
    }

    /**
     * 根据大纲生成详细培训内容
     */
    @PostMapping("/generate-content")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> generateTrainingContent(
            @RequestBody Map<String, String> request) {
        try {
            String outline = request.get("outline");
            String courseTitle = request.get("courseTitle");

            System.out.println("=== AI生成培训内容 ===");
            System.out.println("课程标题: " + courseTitle);

            if (outline == null || outline.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("培训大纲不能为空", null));
            }

            // 调用AI生成内容
            String content = aiService.generateTrainingContent(outline, courseTitle)
                    .block(); // 同步调用

            Map<String, Object> result = new HashMap<>();
            result.put("content", content);
            result.put("outlineLength", outline.length());

            System.out.println("✅ 培训内容生成成功");
            return ResponseEntity.ok(ApiResponse.success("培训内容生成成功", result));

        } catch (Exception e) {
            System.err.println("❌ 生成培训内容失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("生成培训内容失败", e.getMessage()));
        }
    }

    /**
     * 根据培训内容生成考试题目
     */
    @PostMapping("/generate-questions")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> generateExamQuestions(
            @RequestBody Map<String, Object> request) {
        try {
            String courseContent = (String) request.get("courseContent");
            String courseTitle = (String) request.get("courseTitle");
            Integer questionCount = (Integer) request.getOrDefault("questionCount", 10);

            System.out.println("=== AI生成考试题目 ===");
            System.out.println("课程标题: " + courseTitle);
            System.out.println("题目数量: " + questionCount);

            if (courseContent == null || courseContent.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("培训内容不能为空", null));
            }

            // 调用AI生成题目
            String questions = aiService.generateExamQuestions(courseContent, courseTitle, questionCount)
                    .block(); // 同步调用

            Map<String, Object> result = new HashMap<>();
            result.put("questions", questions);
            result.put("questionCount", questionCount);
            result.put("contentLength", courseContent.length());

            System.out.println("✅ 考试题目生成成功");
            return ResponseEntity.ok(ApiResponse.success("考试题目生成成功", result));

        } catch (Exception e) {
            System.err.println("❌ 生成考试题目失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("生成考试题目失败", e.getMessage()));
        }
    }

    /**
     * 根据文档ID生成培训大纲
     */
    @PostMapping("/generate-outline-from-document/{documentId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> generateOutlineFromDocument(
            @PathVariable Long documentId,
            @RequestParam("courseTitle") String courseTitle) {
        try {
            System.out.println("=== 从文档生成培训大纲 ===");
            System.out.println("文档ID: " + documentId);
            System.out.println("课程标题: " + courseTitle);

            // 获取文档
            Optional<Document> documentOpt = documentService.getDocumentById(documentId);
            if (documentOpt.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文档不存在", null));
            }

            Document document = documentOpt.get();
            
            // 简化处理，使用文档标题和描述作为内容
            String documentContent = document.getTitle() + "\n\n" +
                                    (document.getDescription() != null ? document.getDescription() : "");
            if (documentContent.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文档内容为空", null));
            }

            // 调用AI生成大纲
            String outline = aiService.generateTrainingOutline(documentContent, courseTitle)
                    .block(); // 同步调用

            Map<String, Object> result = new HashMap<>();
            result.put("outline", outline);
            result.put("documentTitle", document.getTitle());
            result.put("documentId", documentId);
            result.put("contentLength", documentContent.length());

            System.out.println("✅ 从文档生成培训大纲成功");
            return ResponseEntity.ok(ApiResponse.success("培训大纲生成成功", result));

        } catch (Exception e) {
            System.err.println("❌ 从文档生成培训大纲失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("生成培训大纲失败", e.getMessage()));
        }
    }

    /**
     * 测试AI连接
     */
    @GetMapping("/test-connection")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testAIConnection() {
        try {
            System.out.println("=== 测试AI连接 ===");

            // 简单的测试调用
            String testResponse = aiService.generateSummary("这是一个测试文档，用于验证AI服务连接。", 50)
                    .block();

            Map<String, Object> result = new HashMap<>();
            result.put("connected", true);
            result.put("model", "DeepSeek R1 0528 (free)");
            result.put("testResponse", testResponse);
            result.put("timestamp", System.currentTimeMillis());

            System.out.println("✅ AI连接测试成功");
            return ResponseEntity.ok(ApiResponse.success("AI连接正常", result));

        } catch (Exception e) {
            System.err.println("❌ AI连接测试失败: " + e.getMessage());
            e.printStackTrace();

            Map<String, Object> result = new HashMap<>();
            result.put("connected", false);
            result.put("error", e.getMessage());

            return ResponseEntity.ok(ApiResponse.success("AI连接测试完成", result));
        }
    }

    /**
     * 提取文档内容
     */
    private String extractDocumentContent(MultipartFile file) {
        try {
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                return "";
            }

            String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
            
            switch (extension) {
                case "txt":
                    return new String(file.getBytes(), "UTF-8");
                case "pdf":
                    // 简化处理，实际应该使用PDF解析库
                    return "PDF文档内容提取功能开发中。文件名: " + fileName;
                case "doc":
                case "docx":
                    // 简化处理，实际应该使用Word解析库
                    return "Word文档内容提取功能开发中。文件名: " + fileName;
                default:
                    return "不支持的文件格式: " + extension;
            }
        } catch (Exception e) {
            System.err.println("提取文档内容失败: " + e.getMessage());
            return "";
        }
    }
}
