<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 制药文档管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        制药文档管理系统
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/users" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/documents" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-file-alt me-2"></i>
                                文档管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/system-overview" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-line me-2"></i>
                                系统概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/my-training" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user-graduate me-2"></i>
                                我的培训
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/training-courses" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-graduation-cap me-2"></i>
                                培训课程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/reports" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-bar me-2"></i>
                                报表管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/settings" style="background-color: #0d6efd; color: white !important; border-radius: 0.375rem;">
                                <i class="fas fa-cog me-2"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>

                    <hr>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/profile" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user me-2"></i>
                                个人资料
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">系统设置</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button class="btn btn-success" onclick="saveSettings()">
                                <i class="fas fa-save me-2"></i>保存设置
                            </button>
                            <button class="btn btn-secondary" onclick="resetSettings()">
                                <i class="fas fa-undo me-2"></i>重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Settings Tabs -->
                <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                            <i class="fas fa-cog me-2"></i>基本设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                            <i class="fas fa-shield-alt me-2"></i>安全设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="features-tab" data-bs-toggle="tab" data-bs-target="#features" type="button" role="tab">
                            <i class="fas fa-toggle-on me-2"></i>功能开关
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab">
                            <i class="fas fa-database me-2"></i>备份管理
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="settingsTabContent">
                    <!-- General Settings -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">基本系统设置</h5>
                            </div>
                            <div class="card-body">
                                <form id="generalSettingsForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="systemName" class="form-label">系统名称</label>
                                                <input type="text" class="form-control" id="systemName" value="制药文档管理系统">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="systemVersion" class="form-label">系统版本</label>
                                                <input type="text" class="form-control" id="systemVersion" value="1.0.0" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="maxFileSize" class="form-label">最大文件大小</label>
                                                <select class="form-select" id="maxFileSize">
                                                    <option value="50MB">50MB</option>
                                                    <option value="100MB" selected>100MB</option>
                                                    <option value="200MB">200MB</option>
                                                    <option value="500MB">500MB</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="sessionTimeout" class="form-label">会话超时时间</label>
                                                <select class="form-select" id="sessionTimeout">
                                                    <option value="15分钟">15分钟</option>
                                                    <option value="30分钟" selected>30分钟</option>
                                                    <option value="60分钟">60分钟</option>
                                                    <option value="120分钟">120分钟</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div class="tab-pane fade" id="security" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">安全策略设置</h5>
                            </div>
                            <div class="card-body">
                                <form id="securitySettingsForm">
                                    <div class="mb-3">
                                        <label for="passwordPolicy" class="form-label">密码策略</label>
                                        <textarea class="form-control" id="passwordPolicy" rows="3">最少8位，包含大小写字母和数字</textarea>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="maxLoginAttempts" class="form-label">最大登录尝试次数</label>
                                                <input type="number" class="form-control" id="maxLoginAttempts" value="5" min="3" max="10">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="lockoutDuration" class="form-label">账户锁定时长（分钟）</label>
                                                <input type="number" class="form-control" id="lockoutDuration" value="30" min="5" max="120">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="requirePasswordChange" checked>
                                        <label class="form-check-label" for="requirePasswordChange">
                                            新用户首次登录强制修改密码
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="enableTwoFactor">
                                        <label class="form-check-label" for="enableTwoFactor">
                                            启用双因素认证（开发中）
                                        </label>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Feature Toggles -->
                    <div class="tab-pane fade" id="features" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">功能开关</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="aiAnalysis" checked>
                                            <label class="form-check-label" for="aiAnalysis">
                                                <strong>AI智能分析</strong><br>
                                                <small class="text-muted">启用文档智能分析和分类功能</small>
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="ocrEnabled">
                                            <label class="form-check-label" for="ocrEnabled">
                                                <strong>OCR文字识别</strong><br>
                                                <small class="text-muted">启用图像文字识别功能</small>
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                            <label class="form-check-label" for="emailNotifications">
                                                <strong>邮件通知</strong><br>
                                                <small class="text-muted">启用系统邮件通知功能</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="auditLogging" checked>
                                            <label class="form-check-label" for="auditLogging">
                                                <strong>审计日志</strong><br>
                                                <small class="text-muted">记录所有用户操作日志</small>
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="documentVersioning" checked>
                                            <label class="form-check-label" for="documentVersioning">
                                                <strong>文档版本控制</strong><br>
                                                <small class="text-muted">启用文档版本管理功能</small>
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="autoBackup" checked>
                                            <label class="form-check-label" for="autoBackup">
                                                <strong>自动备份</strong><br>
                                                <small class="text-muted">启用系统自动备份功能</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Backup Management -->
                    <div class="tab-pane fade" id="backup" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">备份管理</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <button class="btn btn-primary w-100" onclick="createBackup()">
                                            <i class="fas fa-download me-2"></i>立即备份
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button class="btn btn-warning w-100" onclick="restoreBackup()">
                                            <i class="fas fa-upload me-2"></i>恢复备份
                                        </button>
                                    </div>
                                </div>
                                
                                <h6>备份历史</h6>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>备份时间</th>
                                                <th>文件大小</th>
                                                <th>类型</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="backupHistory">
                                            <tr>
                                                <td>2025-06-05 08:00:00</td>
                                                <td>125.6 MB</td>
                                                <td>自动备份</td>
                                                <td><span class="badge bg-success">成功</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="downloadBackup('backup_20250605_080000.sql')">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>2025-06-04 08:00:00</td>
                                                <td>123.2 MB</td>
                                                <td>自动备份</td>
                                                <td><span class="badge bg-success">成功</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="downloadBackup('backup_20250604_080000.sql')">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemConfig();
        });

        async function loadSystemConfig() {
            try {
                const response = await authUtils.secureApiCall('/dms/api/settings/config');

                if (response.ok) {
                    const result = await response.json();
                    const config = result.data;
                    
                    // Load general settings
                    document.getElementById('systemName').value = config.systemName || '';
                    document.getElementById('systemVersion').value = config.version || '';
                    document.getElementById('maxFileSize').value = config.maxFileSize || '100MB';
                    document.getElementById('sessionTimeout').value = config.sessionTimeout || '30分钟';
                    
                    // Load feature toggles
                    const features = config.features || {};
                    document.getElementById('aiAnalysis').checked = features.aiAnalysis || false;
                    document.getElementById('ocrEnabled').checked = features.ocrEnabled || false;
                    document.getElementById('emailNotifications').checked = features.emailNotifications || false;
                    document.getElementById('auditLogging').checked = features.auditLogging || false;
                    document.getElementById('documentVersioning').checked = features.documentVersioning || false;
                }
            } catch (error) {
                console.error('Error loading system config:', error);
            }
        }

        async function saveSettings() {
            try {
                const config = {
                    systemName: document.getElementById('systemName').value,
                    maxFileSize: document.getElementById('maxFileSize').value,
                    sessionTimeout: document.getElementById('sessionTimeout').value,
                    passwordPolicy: document.getElementById('passwordPolicy').value,
                    maxLoginAttempts: parseInt(document.getElementById('maxLoginAttempts').value),
                    lockoutDuration: parseInt(document.getElementById('lockoutDuration').value),
                    features: {
                        aiAnalysis: document.getElementById('aiAnalysis').checked,
                        ocrEnabled: document.getElementById('ocrEnabled').checked,
                        emailNotifications: document.getElementById('emailNotifications').checked,
                        auditLogging: document.getElementById('auditLogging').checked,
                        documentVersioning: document.getElementById('documentVersioning').checked,
                        autoBackup: document.getElementById('autoBackup').checked
                    }
                };

                const response = await authUtils.secureApiCall('/dms/api/settings/config', {
                    method: 'POST',
                    body: JSON.stringify(config)
                });

                const result = await response.json();
                if (response.ok) {
                    alert('设置保存成功！');
                } else {
                    alert('保存失败：' + result.message);
                }
            } catch (error) {
                alert('保存失败：网络错误');
                console.error('Error saving settings:', error);
            }
        }

        function resetSettings() {
            if (confirm('确定要重置所有设置吗？')) {
                loadSystemConfig();
                alert('设置已重置');
            }
        }

        function createBackup() {
            if (confirm('确定要创建系统备份吗？这可能需要几分钟时间。')) {
                alert('备份功能开发中...');
            }
        }

        function restoreBackup() {
            if (confirm('确定要恢复备份吗？这将覆盖当前数据！')) {
                alert('恢复功能开发中...');
            }
        }

        function downloadBackup(filename) {
            alert('下载备份功能开发中...');
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 认证工具 -->
    <script src="/dms/js/auth.js"></script>
</body>
</html>
