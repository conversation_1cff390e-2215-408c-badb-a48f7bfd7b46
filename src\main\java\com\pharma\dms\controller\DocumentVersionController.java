package com.pharma.dms.controller;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.Document;
import com.pharma.dms.entity.DocumentVersion;
import com.pharma.dms.entity.User;
import com.pharma.dms.security.UserPrincipal;
import com.pharma.dms.service.DocumentService;
import com.pharma.dms.service.DocumentVersionService;
import com.pharma.dms.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/documents/{documentId}/versions")
public class DocumentVersionController {

    @Autowired
    private DocumentVersionService versionService;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private UserService userService;

    /**
     * 获取文档的版本历史
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Page<Map<String, Object>>>> getVersionHistory(
            @PathVariable Long documentId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Document document = documentService.getDocumentById(documentId)
                    .orElseThrow(() -> new RuntimeException("Document not found"));

            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                       Sort.by(sortBy).descending() : 
                       Sort.by(sortBy).ascending();
            
            Pageable pageable = PageRequest.of(page, size, sort);
            Page<DocumentVersion> versions = versionService.getVersionHistory(document, pageable);

            // 转换为简化的响应数据
            Page<Map<String, Object>> responseData = versions.map(version -> {
                Map<String, Object> versionData = new HashMap<>();
                versionData.put("id", version.getId());
                versionData.put("versionNumber", version.getVersionNumber());
                versionData.put("changeType", version.getChangeType().toString());
                versionData.put("changeDescription", version.getChangeDescription());
                versionData.put("changeReason", version.getChangeReason());
                versionData.put("status", version.getStatus().toString());
                versionData.put("isCurrent", version.getIsCurrent());
                versionData.put("isArchived", version.getIsArchived());
                versionData.put("fileSize", version.getFileSize());
                versionData.put("createdAt", version.getCreatedAt().toString());
                versionData.put("createdBy", Map.of(
                    "id", version.getCreatedBy().getId(),
                    "username", version.getCreatedBy().getUsername(),
                    "fullName", version.getCreatedBy().getFullName()
                ));
                if (version.getApprovedBy() != null) {
                    versionData.put("approvedBy", Map.of(
                        "id", version.getApprovedBy().getId(),
                        "username", version.getApprovedBy().getUsername(),
                        "fullName", version.getApprovedBy().getFullName()
                    ));
                    versionData.put("approvedAt", version.getApprovedAt().toString());
                }
                versionData.put("trainingRequired", version.getTrainingRequired());
                versionData.put("gmpImpactAssessment", version.getGmpImpactAssessment());
                return versionData;
            });

            return ResponseEntity.ok(ApiResponse.success("Version history retrieved", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to retrieve version history", e.getMessage()));
        }
    }

    /**
     * 创建新版本
     */
    @PostMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> createVersion(
            @PathVariable Long documentId,
            @RequestParam("file") MultipartFile file,
            @RequestParam("changeType") String changeTypeStr,
            @RequestParam("changeDescription") String changeDescription,
            @RequestParam(value = "changeReason", required = false) String changeReason,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            Document document = documentService.getDocumentById(documentId)
                    .orElseThrow(() -> new RuntimeException("Document not found"));

            User createdBy = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            DocumentVersion.ChangeType changeType = DocumentVersion.ChangeType.valueOf(changeTypeStr);

            DocumentVersion newVersion = versionService.createVersion(
                document, file, changeType, changeDescription, changeReason, createdBy);

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", newVersion.getId());
            responseData.put("versionNumber", newVersion.getVersionNumber());
            responseData.put("changeType", newVersion.getChangeType().toString());
            responseData.put("status", newVersion.getStatus().toString());
            responseData.put("createdAt", newVersion.getCreatedAt().toString());

            return ResponseEntity.ok(ApiResponse.success("Version created successfully", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to create version", e.getMessage()));
        }
    }

    /**
     * 获取特定版本详情
     */
    @GetMapping("/{versionId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getVersionDetails(
            @PathVariable Long documentId,
            @PathVariable Long versionId) {
        try {
            DocumentVersion version = versionService.getVersionById(versionId)
                    .orElseThrow(() -> new RuntimeException("Version not found"));

            if (!version.getDocument().getId().equals(documentId)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Version does not belong to this document", null));
            }

            Map<String, Object> versionData = new HashMap<>();
            versionData.put("id", version.getId());
            versionData.put("versionNumber", version.getVersionNumber());
            versionData.put("majorVersion", version.getMajorVersion());
            versionData.put("minorVersion", version.getMinorVersion());
            versionData.put("patchVersion", version.getPatchVersion());
            versionData.put("changeType", version.getChangeType().toString());
            versionData.put("changeDescription", version.getChangeDescription());
            versionData.put("changeReason", version.getChangeReason());
            versionData.put("status", version.getStatus().toString());
            versionData.put("isCurrent", version.getIsCurrent());
            versionData.put("isArchived", version.getIsArchived());
            versionData.put("fileSize", version.getFileSize());
            versionData.put("mimeType", version.getMimeType());
            versionData.put("checksum", version.getChecksum());
            versionData.put("createdAt", version.getCreatedAt().toString());
            versionData.put("createdBy", Map.of(
                "id", version.getCreatedBy().getId(),
                "username", version.getCreatedBy().getUsername(),
                "fullName", version.getCreatedBy().getFullName()
            ));
            
            if (version.getApprovedBy() != null) {
                versionData.put("approvedBy", Map.of(
                    "id", version.getApprovedBy().getId(),
                    "username", version.getApprovedBy().getUsername(),
                    "fullName", version.getApprovedBy().getFullName()
                ));
                versionData.put("approvedAt", version.getApprovedAt().toString());
            }
            
            versionData.put("trainingRequired", version.getTrainingRequired());
            versionData.put("gmpImpactAssessment", version.getGmpImpactAssessment());
            versionData.put("effectiveDate", version.getEffectiveDate());
            versionData.put("supersededDate", version.getSupersededDate());
            versionData.put("retentionDate", version.getRetentionDate());
            versionData.put("downloadCount", version.getDownloadCount());
            versionData.put("viewCount", version.getViewCount());

            return ResponseEntity.ok(ApiResponse.success("Version details retrieved", versionData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to retrieve version details", e.getMessage()));
        }
    }

    /**
     * 批准版本
     */
    @PostMapping("/{versionId}/approve")
    @PreAuthorize("hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> approveVersion(
            @PathVariable Long documentId,
            @PathVariable Long versionId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            User approvedBy = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            DocumentVersion version = versionService.approveVersion(versionId, approvedBy);

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", version.getId());
            responseData.put("versionNumber", version.getVersionNumber());
            responseData.put("status", version.getStatus().toString());
            responseData.put("approvedAt", version.getApprovedAt().toString());

            return ResponseEntity.ok(ApiResponse.success("Version approved successfully", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to approve version", e.getMessage()));
        }
    }

    /**
     * 激活版本
     */
    @PostMapping("/{versionId}/activate")
    @PreAuthorize("hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> activateVersion(
            @PathVariable Long documentId,
            @PathVariable Long versionId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            User activatedBy = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            DocumentVersion version = versionService.activateVersion(versionId, activatedBy);

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", version.getId());
            responseData.put("versionNumber", version.getVersionNumber());
            responseData.put("status", version.getStatus().toString());
            responseData.put("effectiveDate", version.getEffectiveDate().toString());

            return ResponseEntity.ok(ApiResponse.success("Version activated successfully", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to activate version", e.getMessage()));
        }
    }

    /**
     * 归档版本
     */
    @PostMapping("/{versionId}/archive")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> archiveVersion(
            @PathVariable Long documentId,
            @PathVariable Long versionId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            User archivedBy = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            DocumentVersion version = versionService.archiveVersion(versionId, archivedBy);

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", version.getId());
            responseData.put("versionNumber", version.getVersionNumber());
            responseData.put("status", version.getStatus().toString());
            responseData.put("isArchived", version.getIsArchived());

            return ResponseEntity.ok(ApiResponse.success("Version archived successfully", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to archive version", e.getMessage()));
        }
    }

    /**
     * 获取版本统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getVersionStatistics(
            @PathVariable Long documentId) {
        try {
            Document document = documentService.getDocumentById(documentId)
                    .orElseThrow(() -> new RuntimeException("Document not found"));

            DocumentVersionService.VersionStatistics stats = versionService.getVersionStatistics(document);

            Map<String, Object> statsData = new HashMap<>();
            statsData.put("totalVersions", stats.getTotalVersions());
            statsData.put("majorVersions", stats.getMajorVersions());
            statsData.put("minorVersions", stats.getMinorVersions());
            statsData.put("patchVersions", stats.getPatchVersions());
            
            if (stats.getLatestVersion() != null) {
                DocumentVersion latest = stats.getLatestVersion();
                statsData.put("latestVersion", Map.of(
                    "id", latest.getId(),
                    "versionNumber", latest.getVersionNumber(),
                    "status", latest.getStatus().toString(),
                    "createdAt", latest.getCreatedAt().toString()
                ));
            }

            return ResponseEntity.ok(ApiResponse.success("Version statistics retrieved", statsData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to retrieve version statistics", e.getMessage()));
        }
    }

    /**
     * 更新GMP影响评估
     */
    @PostMapping("/{versionId}/gmp-assessment")
    @PreAuthorize("hasRole('QA') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> updateGmpAssessment(
            @PathVariable Long documentId,
            @PathVariable Long versionId,
            @RequestBody Map<String, Object> assessmentData,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            User assessor = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            String assessment = (String) assessmentData.get("assessment");
            Boolean trainingRequired = (Boolean) assessmentData.getOrDefault("trainingRequired", false);

            DocumentVersion version = versionService.updateGmpAssessment(
                versionId, assessment, trainingRequired, assessor);

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", version.getId());
            responseData.put("versionNumber", version.getVersionNumber());
            responseData.put("gmpImpactAssessment", version.getGmpImpactAssessment());
            responseData.put("trainingRequired", version.getTrainingRequired());

            return ResponseEntity.ok(ApiResponse.success("GMP assessment updated successfully", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to update GMP assessment", e.getMessage()));
        }
    }
}
