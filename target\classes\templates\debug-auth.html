<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证调试 - 制药DMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>认证状态调试</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>LocalStorage状态</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Token:</strong> <span id="tokenStatus">检查中...</span></p>
                        <p><strong>Token长度:</strong> <span id="tokenLength">检查中...</span></p>
                        <p><strong>Token前缀:</strong> <span id="tokenPrefix">检查中...</span></p>
                        <p><strong>User数据:</strong> <span id="userStatus">检查中...</span></p>
                        <p><strong>用户名:</strong> <span id="username">检查中...</span></p>
                        <p><strong>角色:</strong> <span id="roles">检查中...</span></p>
                        
                        <button class="btn btn-primary" onclick="checkStatus()">重新检查</button>
                        <button class="btn btn-warning" onclick="clearStorage()">清除存储</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>API测试</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-info mb-2" onclick="testAPI('/dms/api/auth/validate')">测试认证验证</button><br>
                        <button class="btn btn-info mb-2" onclick="testAPI('/dms/api/users/me')">测试获取用户信息</button><br>
                        <button class="btn btn-info mb-2" onclick="testAPI('/dms/api/documents?page=0&size=1')">测试文档API</button><br>
                        <button class="btn btn-warning mb-2" onclick="testTokenDirectly()">直接测试Token</button><br>
                        
                        <div id="apiResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>快速登录</h5>
                    </div>
                    <div class="card-body">
                        <form id="quickLoginForm">
                            <div class="row">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="quickUsername" placeholder="用户名" value="admin">
                                </div>
                                <div class="col-md-3">
                                    <input type="password" class="form-control" id="quickPassword" placeholder="密码" value="admin123">
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-success" onclick="quickLogin()">快速登录</button>
                                </div>
                                <div class="col-md-3">
                                    <a href="/dms/profile" class="btn btn-secondary">访问个人资料</a>
                                </div>
                            </div>
                        </form>
                        <div id="loginResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function checkStatus() {
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            
            document.getElementById('tokenStatus').textContent = token ? '存在' : '不存在';
            document.getElementById('tokenLength').textContent = token ? token.length : '0';
            document.getElementById('tokenPrefix').textContent = token ? token.substring(0, 30) + '...' : '无';
            document.getElementById('userStatus').textContent = user ? '存在' : '不存在';
            
            if (user) {
                try {
                    const userObj = JSON.parse(user);
                    document.getElementById('username').textContent = userObj.username || '未知';
                    document.getElementById('roles').textContent = userObj.roles ? userObj.roles.join(', ') : '无';
                } catch (e) {
                    document.getElementById('username').textContent = '解析错误';
                    document.getElementById('roles').textContent = '解析错误';
                }
            } else {
                document.getElementById('username').textContent = '无';
                document.getElementById('roles').textContent = '无';
            }
        }

        function clearStorage() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            checkStatus();
            alert('存储已清除');
        }

        async function testAPI(url) {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<div class="alert alert-info">测试中...</div>';
            
            const token = localStorage.getItem('token');
            if (!token) {
                resultsDiv.innerHTML = '<div class="alert alert-danger">没有token，无法测试API</div>';
                return;
            }
            
            try {
                const response = await fetch(url, {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });
                
                const statusClass = response.ok ? 'success' : 'danger';
                const result = await response.text();
                
                resultsDiv.innerHTML = `
                    <div class="alert alert-${statusClass}">
                        <strong>URL:</strong> ${url}<br>
                        <strong>状态:</strong> ${response.status} ${response.statusText}<br>
                        <strong>响应:</strong> ${result.substring(0, 200)}...
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="alert alert-danger">错误: ${error.message}</div>`;
            }
        }

        async function quickLogin() {
            const username = document.getElementById('quickUsername').value;
            const password = document.getElementById('quickPassword').value;

            document.getElementById('loginResult').innerHTML =
                '<div class="alert alert-info">登录中...</div>';

            try {
                console.log('🔐 尝试登录:', username);

                const response = await fetch('/dms/api/auth/signin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                console.log('📡 登录响应状态:', response.status);
                const result = await response.json();
                console.log('📦 登录响应数据:', result);

                if (response.ok) {
                    // 检查响应数据结构
                    let token, userData;

                    if (result.token) {
                        // 直接返回token的情况
                        token = result.token;
                        userData = result;
                    } else if (result.data && result.data.token) {
                        // 包装在data中的情况
                        token = result.data.token;
                        userData = result.data;
                    } else {
                        // 尝试其他可能的字段
                        token = result.accessToken || result.jwt;
                        userData = result;
                    }

                    if (token) {
                        localStorage.setItem('token', token);
                        localStorage.setItem('user', JSON.stringify(userData));

                        console.log('✅ Token已保存:', token.substring(0, 20) + '...');
                        console.log('👤 用户数据已保存:', userData.username);

                        document.getElementById('loginResult').innerHTML =
                            '<div class="alert alert-success">登录成功！Token已保存</div>';

                        checkStatus();
                    } else {
                        document.getElementById('loginResult').innerHTML =
                            '<div class="alert alert-warning">登录成功但未找到token字段</div>';
                    }
                } else {
                    document.getElementById('loginResult').innerHTML =
                        '<div class="alert alert-danger">登录失败：' + (result.message || result.error || '未知错误') + '</div>';
                }
            } catch (error) {
                console.error('❌ 登录异常:', error);
                document.getElementById('loginResult').innerHTML =
                    '<div class="alert alert-danger">登录错误：' + error.message + '</div>';
            }
        }

        async function testTokenDirectly() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<div class="alert alert-info">直接测试Token...</div>';

            const token = localStorage.getItem('token');
            if (!token) {
                resultsDiv.innerHTML = '<div class="alert alert-danger">没有Token可测试</div>';
                return;
            }

            console.log('=== 直接Token测试 ===');
            console.log('Token长度:', token.length);
            console.log('Token内容:', token);
            console.log('Token前50字符:', token.substring(0, 50));

            // 测试多个API端点
            const testUrls = [
                '/dms/api/auth/validate',
                '/dms/api/users/me',
                '/dms/api/documents?page=0&size=1',
                '/dms/api/ai/ocr/batch-extract'
            ];

            let results = '<div class="alert alert-info"><strong>Token测试结果:</strong><br>';
            results += `Token长度: ${token.length}<br>`;
            results += `Token前缀: ${token.substring(0, 50)}...<br><br>`;

            for (const url of testUrls) {
                try {
                    console.log('测试URL:', url);
                    const response = await fetch(url, {
                        method: url.includes('batch-extract') ? 'POST' : 'GET',
                        headers: {
                            'Authorization': 'Bearer ' + token,
                            'Content-Type': 'application/json'
                        },
                        body: url.includes('batch-extract') ? new FormData() : undefined
                    });

                    const statusClass = response.ok ? 'success' : 'danger';
                    results += `<span class="badge bg-${statusClass}">${response.status}</span> ${url}<br>`;

                    if (!response.ok) {
                        try {
                            const errorText = await response.text();
                            console.log(`${url} 错误响应:`, errorText);
                        } catch (e) {
                            console.log(`${url} 无法读取错误响应`);
                        }
                    }
                } catch (error) {
                    results += `<span class="badge bg-warning">ERROR</span> ${url} - ${error.message}<br>`;
                    console.error(`${url} 异常:`, error);
                }
            }

            results += '</div>';
            resultsDiv.innerHTML = results;
        }

        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', function() {
            checkStatus();
        });
    </script>
</body>
</html>
