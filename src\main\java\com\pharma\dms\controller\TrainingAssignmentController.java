package com.pharma.dms.controller;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.TrainingAssignment;
import com.pharma.dms.entity.TrainingCourse;
import com.pharma.dms.entity.User;
import com.pharma.dms.repository.TrainingCourseRepository;
import com.pharma.dms.security.UserPrincipal;
import com.pharma.dms.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/training/assignments")
public class TrainingAssignmentController {

    @Autowired
    private UserService userService;

    @Autowired
    private TrainingCourseRepository courseRepository;

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * 批量分配培训
     */
    @PostMapping("/assign/batch")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    @Transactional
    public ResponseEntity<ApiResponse<Map<String, Object>>> assignTrainingBatch(
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 批量分配培训开始 ===");
            System.out.println("请求数据: " + request);

            Long courseId = Long.valueOf(request.get("courseId").toString());
            @SuppressWarnings("unchecked")
            List<Integer> userIdInts = (List<Integer>) request.get("userIds");
            List<Long> userIds = userIdInts.stream().map(Long::valueOf).toList();
            String dueDateStr = (String) request.get("dueDate");
            String notes = (String) request.get("notes");

            User assignedBy = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            TrainingCourse course = courseRepository.findById(courseId)
                    .orElseThrow(() -> new RuntimeException("Course not found"));

            LocalDateTime dueDate = dueDateStr != null && !dueDateStr.isEmpty() ? 
                                   LocalDateTime.parse(dueDateStr) : null;

            int assignedCount = 0;
            for (Long userId : userIds) {
                try {
                    User user = userService.getUserById(userId)
                            .orElseThrow(() -> new RuntimeException("User not found: " + userId));

                    // 检查是否已经分配过
                    String checkQuery = "SELECT COUNT(ta) FROM TrainingAssignment ta WHERE ta.course.id = :courseId AND ta.user.id = :userId";
                    Long existingCount = entityManager.createQuery(checkQuery, Long.class)
                            .setParameter("courseId", courseId)
                            .setParameter("userId", userId)
                            .getSingleResult();

                    if (existingCount > 0) {
                        System.out.println("用户 " + userId + " 已经分配过课程 " + courseId);
                        continue; // 跳过已分配的用户
                    }

                    TrainingAssignment assignment = new TrainingAssignment(course, user, assignedBy);
                    assignment.setDueDate(dueDate);
                    assignment.setNotes(notes);
                    assignment.setIsMandatory(true);

                    entityManager.persist(assignment);
                    assignedCount++;
                    
                    System.out.println("成功分配给用户: " + user.getUsername());
                } catch (Exception e) {
                    System.out.println("分配给用户 " + userId + " 失败: " + e.getMessage());
                }
            }

            entityManager.flush();

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("assignedCount", assignedCount);
            responseData.put("totalRequested", userIds.size());
            responseData.put("courseId", courseId);
            responseData.put("courseName", course.getTitle());

            System.out.println("批量分配完成，成功分配: " + assignedCount + " 人");
            return ResponseEntity.ok(ApiResponse.success("Batch training assignment completed", responseData));
        } catch (Exception e) {
            System.out.println("批量分配失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to assign training batch", e.getMessage()));
        }
    }

    /**
     * 按部门分配培训
     */
    @PostMapping("/assign/department")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    @Transactional
    public ResponseEntity<ApiResponse<Map<String, Object>>> assignTrainingToDepartment(
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 按部门分配培训开始 ===");
            
            Long courseId = Long.valueOf(request.get("courseId").toString());
            Long departmentId = Long.valueOf(request.get("departmentId").toString());
            String dueDateStr = (String) request.get("dueDate");
            String notes = (String) request.get("notes");

            User assignedBy = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            TrainingCourse course = courseRepository.findById(courseId)
                    .orElseThrow(() -> new RuntimeException("Course not found"));

            LocalDateTime dueDate = dueDateStr != null && !dueDateStr.isEmpty() ? 
                                   LocalDateTime.parse(dueDateStr) : null;

            // 获取部门用户
            List<User> departmentUsers = userService.getUsersByDepartment(departmentId);
            
            int assignedCount = 0;
            for (User user : departmentUsers) {
                try {
                    // 检查是否已经分配过
                    String checkQuery = "SELECT COUNT(ta) FROM TrainingAssignment ta WHERE ta.course.id = :courseId AND ta.user.id = :userId";
                    Long existingCount = entityManager.createQuery(checkQuery, Long.class)
                            .setParameter("courseId", courseId)
                            .setParameter("userId", user.getId())
                            .getSingleResult();

                    if (existingCount > 0) {
                        continue; // 跳过已分配的用户
                    }

                    TrainingAssignment assignment = new TrainingAssignment(course, user, assignedBy);
                    assignment.setDueDate(dueDate);
                    assignment.setNotes(notes);
                    assignment.setIsMandatory(true);

                    entityManager.persist(assignment);
                    assignedCount++;
                } catch (Exception e) {
                    System.out.println("分配给用户 " + user.getUsername() + " 失败: " + e.getMessage());
                }
            }

            entityManager.flush();

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("assignedCount", assignedCount);
            responseData.put("departmentId", departmentId);
            responseData.put("totalUsers", departmentUsers.size());

            return ResponseEntity.ok(ApiResponse.success("Department training assignment completed", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to assign training to department", e.getMessage()));
        }
    }

    /**
     * 按角色分配培训
     */
    @PostMapping("/assign/role")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    @Transactional
    public ResponseEntity<ApiResponse<Map<String, Object>>> assignTrainingToRole(
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 按角色分配培训开始 ===");
            
            Long courseId = Long.valueOf(request.get("courseId").toString());
            String roleName = (String) request.get("roleName");
            String dueDateStr = (String) request.get("dueDate");
            String notes = (String) request.get("notes");

            User assignedBy = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            TrainingCourse course = courseRepository.findById(courseId)
                    .orElseThrow(() -> new RuntimeException("Course not found"));

            LocalDateTime dueDate = dueDateStr != null && !dueDateStr.isEmpty() ? 
                                   LocalDateTime.parse(dueDateStr) : null;

            // 获取角色用户 - 简化实现，获取所有用户
            List<User> allUsers = userService.getAllUsers();
            
            int assignedCount = 0;
            for (User user : allUsers) {
                try {
                    // 简化角色检查 - 实际应该检查用户角色
                    if (!user.getIsActive()) {
                        continue; // 跳过非活跃用户
                    }

                    // 检查是否已经分配过
                    String checkQuery = "SELECT COUNT(ta) FROM TrainingAssignment ta WHERE ta.course.id = :courseId AND ta.user.id = :userId";
                    Long existingCount = entityManager.createQuery(checkQuery, Long.class)
                            .setParameter("courseId", courseId)
                            .setParameter("userId", user.getId())
                            .getSingleResult();

                    if (existingCount > 0) {
                        continue; // 跳过已分配的用户
                    }

                    TrainingAssignment assignment = new TrainingAssignment(course, user, assignedBy);
                    assignment.setDueDate(dueDate);
                    assignment.setNotes(notes);
                    assignment.setIsMandatory(true);

                    entityManager.persist(assignment);
                    assignedCount++;
                } catch (Exception e) {
                    System.out.println("分配给用户 " + user.getUsername() + " 失败: " + e.getMessage());
                }
            }

            entityManager.flush();

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("assignedCount", assignedCount);
            responseData.put("roleName", roleName);

            return ResponseEntity.ok(ApiResponse.success("Role training assignment completed", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to assign training to role", e.getMessage()));
        }
    }

    /**
     * 获取用户的培训分配
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getUserAssignments(
            @PathVariable Long userId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            // 检查权限：用户只能查看自己的分配，管理员可以查看所有
            User currentUser = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            if (!currentUser.getId().equals(userId) && 
                !userPrincipal.getAuthorities().stream()
                    .anyMatch(auth -> auth.getAuthority().equals("ROLE_ADMIN") || 
                                    auth.getAuthority().equals("ROLE_QA"))) {
                return ResponseEntity.status(403)
                        .body(ApiResponse.error("Access denied", null));
            }

            String query = "SELECT ta FROM TrainingAssignment ta WHERE ta.user.id = :userId ORDER BY ta.assignedDate DESC";
            @SuppressWarnings("unchecked")
            List<TrainingAssignment> assignments = entityManager.createQuery(query)
                    .setParameter("userId", userId)
                    .getResultList();

            List<Map<String, Object>> responseData = assignments.stream().map(assignment -> {
                Map<String, Object> assignmentData = new HashMap<>();
                assignmentData.put("id", assignment.getId());
                assignmentData.put("course", Map.of(
                    "id", assignment.getCourse().getId(),
                    "title", assignment.getCourse().getTitle(),
                    "description", assignment.getCourse().getDescription()
                ));
                assignmentData.put("status", assignment.getStatus().toString());
                assignmentData.put("assignedDate", assignment.getAssignedDate().toString());
                assignmentData.put("dueDate", assignment.getDueDate() != null ? assignment.getDueDate().toString() : null);
                assignmentData.put("completionDate", assignment.getCompletionDate() != null ? assignment.getCompletionDate().toString() : null);
                assignmentData.put("isOverdue", assignment.isOverdue());
                assignmentData.put("isCompleted", assignment.isCompleted());
                assignmentData.put("priority", assignment.getPriority().toString());
                assignmentData.put("isMandatory", assignment.getIsMandatory());
                return assignmentData;
            }).toList();

            return ResponseEntity.ok(ApiResponse.success("User assignments retrieved", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to retrieve user assignments", e.getMessage()));
        }
    }

    /**
     * 完成培训
     */
    @PostMapping("/{assignmentId}/complete")
    @PreAuthorize("hasRole('USER')")
    @Transactional
    public ResponseEntity<ApiResponse<Map<String, Object>>> completeTraining(
            @PathVariable Long assignmentId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            TrainingAssignment assignment = entityManager.find(TrainingAssignment.class, assignmentId);
            if (assignment == null) {
                return ResponseEntity.notFound().build();
            }

            assignment.markCompleted();
            entityManager.merge(assignment);

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", assignment.getId());
            responseData.put("status", assignment.getStatus().toString());
            responseData.put("completionDate", assignment.getCompletionDate().toString());

            return ResponseEntity.ok(ApiResponse.success("Training completed", responseData));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to complete training", e.getMessage()));
        }
    }
}
