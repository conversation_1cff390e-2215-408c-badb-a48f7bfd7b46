<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证测试 - 制药DMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>认证测试页面</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Token信息</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Token存在:</strong> <span id="tokenExists">检查中...</span></p>
                        <p><strong>用户信息:</strong> <span id="userInfo">检查中...</span></p>
                        <button class="btn btn-primary" onclick="checkToken()">检查Token</button>
                        <button class="btn btn-warning" onclick="clearToken()">清除Token</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>API测试</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success mb-2" onclick="testValidate()">测试Token验证</button><br>
                        <button class="btn btn-info mb-2" onclick="testAuth()">测试认证端点</button><br>
                        <button class="btn btn-secondary mb-2" onclick="testOCR()">测试OCR端点</button><br>
                        <button class="btn btn-primary mb-2" onclick="testUsers()">测试用户端点</button><br>
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>测试日志</h5>
                    </div>
                    <div class="card-body">
                        <div id="logOutput" style="height: 300px; overflow-y: scroll; background: #f8f9fa; padding: 10px; font-family: monospace;"></div>
                        <button class="btn btn-outline-secondary mt-2" onclick="clearLog()">清除日志</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/dms/js/auth.js"></script>
    
    <script>
        function log(message) {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function checkToken() {
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            
            document.getElementById('tokenExists').textContent = token ? '是' : '否';
            document.getElementById('userInfo').textContent = user ? JSON.parse(user).username : '无';
            
            log(`Token检查: ${token ? '存在' : '不存在'}`);
            if (token) {
                log(`Token长度: ${token.length}`);
                log(`Token前20字符: ${token.substring(0, 20)}...`);
            }
        }

        function clearToken() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            checkToken();
            log('Token已清除');
        }

        async function testValidate() {
            log('测试Token验证...');
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    log('错误: 没有Token');
                    return;
                }

                const response = await fetch('/dms/api/auth/validate', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });

                const result = await response.json();
                log(`验证结果: ${response.status} - ${result.message}`);
                
                document.getElementById('testResults').innerHTML = 
                    `<div class="alert alert-${response.ok ? 'success' : 'danger'}">
                        验证结果: ${result.message}
                    </div>`;
            } catch (error) {
                log(`验证错误: ${error.message}`);
            }
        }

        async function testAuth() {
            log('测试认证端点...');
            try {
                const response = await authUtils.secureApiCall('/dms/api/auth/test');
                if (response) {
                    const result = await response.json();
                    log(`认证测试结果: ${response.status} - ${result.message}`);
                    
                    document.getElementById('testResults').innerHTML = 
                        `<div class="alert alert-${response.ok ? 'success' : 'danger'}">
                            认证测试: ${result.message}
                        </div>`;
                }
            } catch (error) {
                log(`认证测试错误: ${error.message}`);
            }
        }

        async function testOCR() {
            log('测试OCR端点...');
            try {
                const response = await authUtils.secureApiCall('/dms/api/ai/status');
                if (response) {
                    const result = await response.json();
                    log(`OCR状态: ${response.status} - ${JSON.stringify(result.data)}`);
                    
                    document.getElementById('testResults').innerHTML = 
                        `<div class="alert alert-${response.ok ? 'success' : 'danger'}">
                            OCR状态: ${response.ok ? '可用' : '不可用'}
                        </div>`;
                }
            } catch (error) {
                log(`OCR测试错误: ${error.message}`);
            }
        }

        async function testUsers() {
            log('测试用户端点...');
            try {
                const response = await authUtils.secureApiCall('/dms/api/users/active');
                if (response) {
                    const result = await response.json();
                    log(`用户端点: ${response.status} - 返回${result.data ? result.data.length : 0}个用户`);
                    
                    document.getElementById('testResults').innerHTML = 
                        `<div class="alert alert-${response.ok ? 'success' : 'danger'}">
                            用户端点: ${response.ok ? '正常' : '失败'}
                        </div>`;
                }
            } catch (error) {
                log(`用户测试错误: ${error.message}`);
            }
        }

        function clearLog() {
            document.getElementById('logOutput').innerHTML = '';
        }

        // 页面加载时检查
        document.addEventListener('DOMContentLoaded', function() {
            checkToken();
            log('认证测试页面已加载');
        });
    </script>
</body>
</html>
